  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        Student List
       
        <select class="form-control" id="select_year" style="width:150px;">
        	<option value="">Select Batch</option>
            <?php  foreach($stu_year as $yr){ ?>
            	<option value="<?php echo $yr->batch; ?>" <?php echo !empty($sel_yr) && $sel_yr == $yr->batch?'selected':''; ?>><?php echo $yr->batch; ?></option>
            <?php } ?>
        </select>
      
        <!--<small>it all starts here</small>-->
      </h1>
      <ol class="breadcrumb">
        <li><a href="<?php echo base_url('admin/dashboard'); ?>"><i class="fa fa-dashboard"></i> Dashboard</a></li>
        <li class="active">Student List</li>
      </ol>
    </section>

    <!-- Main content -->
    <section class="content">

      <!-- Default box -->
      <div class="box">
     
        <div class="box-body">
			<table id="tbl_stu_set" class="table table-bordered table-striped">
                <thead>
                <tr>
                  <th>Regno</th>
                  <th>Name</th>
                  <th>Email</th>
                  <th>Password</th>
                  <th>Action</th>
                  
                </tr>
                </thead>
                <tbody>
                <?php  foreach($students as $stu){?>
                    <tr>
                      <td><?php echo $stu->regd_no; ?></td>
                      <td><?php echo $stu->name; ?></td>
                      <td><?php echo $stu->email; ?></td>
                       <td><?php echo $this->encryption->decrypt($stu->password); ?></td>
                       <td><a class="btn btn-sm btn-info" onclick="getStudentInfo('<?php echo $stu->regd_no; ?>')"   title="Modify Student Name"><i class="fa fa-edit" aria-hidden="true"></i>
                                </a></td>
                    </tr>
               <?php } ?>
                
                </tbody>
                <!--<tfoot>
                <tr>
                  <th>Category Name</th>
                  <th>Task</th>
                  
                </tr>
                </tfoot>-->
              </table>

          
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
             
        </div>
        <!-- /.box-footer-->
    
      
      </div>
      <!-- /.box -->

    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
  
  <div class="modal fade" id="student-detail-modal">
      <div class="modal-dialog modal-sm">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">Student Name</h4>
          </div>
          <div class="modal-body" id="stud-info">
            <p><i class='fa fa-spinner fa-spin'></i></p>
            
            
            
            
          </div>
          
<!--          <div class="modal-footer">
            <button type="button" class="btn btn-default pull-left" data-dismiss="modal">Close</button>
          </div>-->
        </div>
        <!-- /.modal-content -->
      </div>
      <!-- /.modal-dialog -->
    </div>
    <!-- /.modal -->
  
  <script>
  	$(document).ready(function(e) {
       $('#tbl_stu_set').DataTable({
			"pageLength": 150,
			'paging'      : true,
			'searching'   : true,
			'ordering'    : true,
			'info'        : true,
			'autoWidth'   : false,
			"bLengthChange": false,
			"order": []
		
		});
		
		
		$('#select_year').change(function(e) {
            var yr = $(this).val();
			window.location.href="<?php echo base_url('admin/students') ?>" +"/" + yr;
			
        });

    });
	
	function getStudentInfo(rollno){
		var params = {
			"rollno" :rollno
		};
		$('#student-detail-modal').modal('show', {backdrop: 'static'});
		$.get('<?php echo base_url("admin/getstudentbyregno") ?>', params, function (html) {
			$('.modal-title').html('Modify Student Name');
			$('#stud-info').html(html);
		}); 

	}
	
	
	$(document).on('submit', '#frmStudInfo', function(e) {
		e.preventDefault();
		
				$.ajax({
				type: "POST",
				url: "<?php echo base_url('admin/savestudent') ?>",
				data: $("#frmStudInfo").serialize(),
				dataType: 'json',
				beforeSend: function (xhr) {
				  $("#btnsave").prop("disabled",true);
				  $("#btnsave").html('<i class="fa fa-spinner fa-spin"></i> Saving...');
				  
				},
				success: function (res_data) {
					if (res_data[0].resSuccess == 1) { 
							bootbox.alert({ 
								size: "small",
								message: "Data saved successfully",
								callback:function(){
										//window.location.href="<?php echo base_url('admin/resultset') ?>";
										window.location.reload();
									}
										
								});	
					}else if (res_data[0].resSuccess == 2){
						  $("#btnsave").prop("disabled",false);
						  $("#btnsave").html('Confirm and Save');
						   bootbox.alert({ 
								size: "small",
								message: res_data[0].msg,
								callback:function(){
										//window.location.href=window.location.href;
									}
							});	
				
							return false;
						}					
					}
				});
			
		return false;
		});
  
  </script>