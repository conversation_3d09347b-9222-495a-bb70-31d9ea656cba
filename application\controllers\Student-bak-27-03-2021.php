<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Student extends CI_Controller {
	
	function __construct() {
        parent::__construct();
		
		$this->load->helper('text');
		$this->load->library(array('encryption', 'form_validation'));
		$this->load->model('student_model', 'studentModel');
		
		//echo $this->encryption->encrypt('admin'); die();

	}
	
		
	public function index(){ 
		$this->load->view('_student/login');
	}
	
	public function login(){
		$result = array();
		$this->form_validation->set_rules('regdno', "regdno", 'trim|required|callback__validate_login');
		if ($this->form_validation->run() == FALSE){
			$result[] = array('resSuccess' => 2, 'msg' => 'Error', 'errtype' => 'Validation', 'arrError' => validation_errors());
			echo json_encode($result);			
			exit();
		}else{
			$result[] = array('resSuccess' => 1, 'msg' => 'Success');
			echo json_encode($result);
			exit();				
		}

		
	}
	
	public function _validate_login(){
		
		$regdno = trim($this->input->post("regdno"));
		$pwd = trim($this->input->post("password"));

		if($regdno!="" && $pwd!=""){
			if($this->studentModel->validate_login($regdno,$pwd)){
				return true;	
			}else{
				$this->form_validation->set_message('_validate_login', 'Unauthenticated User! Access Denied.');
          		return false;
			}
		}
	}
	
	
	
	public function logout(){
		
		$this->session->sess_destroy();
		redirect('student/');

	}
	
	public function dashboard(){
		
		$this->load->library('Student_acess');
		$data["menu"] = "dashboard";
		$this->load->view('_student_shared/header',$data);	
		$this->load->view('_student/dashboard',$data);
		$this->load->view('_student_shared/footer',$data);
	}
	
	
	public function result(){
		
		$this->load->library('Student_acess');
		$data["menu"] = "result";
		
		$data['results'] = $this->studentModel->getResult();
		
		
		
		$this->load->view('_student_shared/header',$data);	
		$this->load->view('_student/resultlist',$data);
		$this->load->view('_student_shared/footer',$data);
	}
	
	
	public function resultdetails($res_id){
		$this->load->library('Student_acess');
		
		$data["menu"] = "result";
		$data['result'] = $this->studentModel->getStudentResult($res_id);
		
		//print_r($data['results']); die();
		//$this->load->view('_student_shared/header',$data);	
		$this->load->view('_student/result',$data);
		//$this->load->view('_student_shared/footer',$data);
		
	}
	
	
	public function changepwd(){
		$this->load->library('Student_acess');
		$data["menu"] = "";
		$this->load->view('_student_shared/header',$data);	
		$this->load->view('_student/changepwd',$data);
		$this->load->view('_student_shared/footer',$data);
		
	}
	
	
	
	public function saveprofile(){
		$this->load->library('Student_acess');
		$data["menu"] = "";

		$name = $this->input->post('name',TRUE);
		$email = $this->input->post('email',TRUE);
		$old_password = $this->input->post('old_password',TRUE);
		$new_password = $this->input->post('new_password',TRUE);
		
		
		$this->db->trans_strict(TRUE);	
		$this->db->trans_begin();

			$profile_info = $this->adminModel->getAdmindetails();
			if(!empty($profile_info)){
				
				if($this->encryption->decrypt($profile_info->password) != $old_password){
					$result[] = array('resSuccess' => 2, 'msg' => 'Incorrect old password provided');
					echo json_encode($result);			
					exit();
				}else{
					
					if(!empty($new_password)){
						
						$data_pwd = array('password' => $this->encryption->encrypt($new_password));
						$this->adminModel->updateProfile($data_pwd);
					}
				}
				
			}else{
					$result[] = array('resSuccess' => 2, 'msg' => 'Profile not found.');
					echo json_encode($result);			
					exit();
			}
			
		
		if($this->db->trans_status() === TRUE){
			$this->db->trans_commit(); 
			$result[] = array('resSuccess' => 1, 'msg' => 'Profile saved successfully');
			echo json_encode($result);
			exit();				
			
		}else{
			 $this->db->trans_rollback(); 
			$result[] = array('resSuccess' => 2, 'msg' => 'Unable to save profile');
			echo json_encode($result);			
			exit();
		}	
		
			
		
	}
	
	
	/*public function rechecking(){
		$this->load->library('Student_acess');
		$data["menu"] = "rechecking";	
		
	}*/
	

}
