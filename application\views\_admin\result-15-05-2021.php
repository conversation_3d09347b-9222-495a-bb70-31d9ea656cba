<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=yes" name="viewport">
  <!-- Bootstrap 3.3.7 -->
  <!-- Font Awesome -->
  <link rel="stylesheet" href="<?php echo base_url('assets/bower_components/font-awesome/css/font-awesome.min.css') ?>">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,600,700,300italic,400italic,600italic">
<title><?php echo APP_TITLE; ?></title>
<style>
body {
    font-family: 'Source Sans Pro','Helvetica Neue',Helvetica,Arial,sans-serif;
    font-weight: 400;
    overflow-x: hidden;
    overflow-y: auto;
}
.main-table{
  border: 1px solid black;
  border-collapse: collapse;
  max-width:680px;
  min-width:680px;
  padding:5px;
  font-family:sans-serif;
  font-size:13px;
}
.btn {
    display: inline-block;
    margin-bottom: 0;
    font-weight: 400;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    cursor: pointer;
    background-image: none;
    border: 1px solid transparent;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.42857143;
    border-radius: 4px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
.btn-primary {
    background-color: #3c8dbc;
    border-color: #367fa9;
	color:#fff;
}
.btn-default {
    background-color: #f4f4f4;
    color: #444;
    border-color: #ddd;
}
a { text-decoration:none;}
@media print {
         .print-column{ display:none;}
}

</style>
</head>

<body>
	<table class="main-table" align="center">
    <tr>
    	<td align="center">
        	<img src="https://www.cet.edu.in/images/home_04.jpg" alt="Logo" style="width:45px;"/>
        </td>
    </tr>
    <tr>
    	<td></td>
    </tr>
    <tr align="center">
    	<td>COLLEGE OF ENGINEERING AND TECHNOLOGY<br /><span style="color:#C8000C;">(AUTONOMOUS)</span></td>
    </tr>
	<tr>
    	<td style="background-color:#002060;color:#fff; padding:5px;"><?php echo $result[0]->title; ?></td>
	</tr>
    <tr>
    	<td>
        	<table width="100%">
            	<tr>
                	<td width="110px">Registration No:</td>
                    <td style="color:#1C9AC2;font-weight:bold;"><?php echo $result[0]->regd_no; ?></td>
                </tr>
                <tr>
                	<td width="110px;">Student name:</td>
                	<td style="color:#003399;font-weight:bold;"><?php echo $result[0]->name; ?></td>
                </tr>
                <tr>
                	<td width="110px;">Branch:</td>
                    <td style="color:#2A9630;font-weight:bold;"><?php echo $result[0]->branch; ?></td>
                </tr>
            </table>
        </td>
    </tr>
    <tr>
    	<td>
        	<table width="100%">
            	<tr>
                	<th align="left" width="10%">SL. No.</th>
                    <th align="left" width="17%">Subject Code</th>
                    <th align="left" width="53%">Subject</th>
                    <th align="center" width="10%">Credit</th>
                    <th align="center" width="10%">Grade</th>
                </tr>
                <?php $i=1; $tot_credits = 0; foreach($result as $res){ $tot_credits+= $res->credits; ?>
                    <tr>
                        <td align="center"><?php echo $i ?></td>
                        <td><?php echo $res->subject_code ?></td>
                        <td><?php echo $res->subject_name ?></td>
                        <td align="center"><?php echo $res->credits ?></td>
                        <td align="center"><?php echo $res->grade ?></td>
                    </tr>
                <?php $i++; } ?>
                <tr>
                	<td colspan="3" align="right">Total Credits:</td>
                    <td colspan="2" align="left">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?php echo $tot_credits; ?></td>
                </tr>
                <tr>
                	<td colspan="3"><strong>Published On:</strong>  <?php echo date('d/m/Y',strtotime($res->publish_date)) ?></td>
                    <td colspan="2" align="right">SGPA:<?php echo $result[0]->sgpa; ?></td>
                </tr>
            </table>
    	</td>
    </tr>
    <tr>
    	<td>
        	<table width="100%" style="padding:10px;">
            	<?php if($result[0]->rechecking_allowed == '0'){?>
                    <tr>
                        <td width="3%" valign="top">1.</td>
                        <td width="73%" valign="top"> The result is provisional.</td>
                    </tr>
                    
                 <tr>   
                    <td width="3%" valign="top">2. </td>
                    <td width="73%" valign="top"> The SGPA shown for the subjects dispalyed on this page.</td>
                </tr>
                
                <?php }else{ ?>
            
                <tr>
                	<td width="3%" valign="top">1.</td>
                    <td width="73%" valign="top"> The result is provisional.</td>
                </tr>
                <tr>    
                    <td width="3%" valign="top">2. </td>
                    <td width="73%" valign="top"> The student interested for retotaling/ rechecking are to apply through online mode on or before <?php echo !empty($result[0]->end_date)?date('d.m.Y',strtotime($result[0]->end_date)):'' ?> by paying Rs. 200 per answer script to the college. </td>
               	</tr>
                <tr>
                	<td width="3%">3.</td>
                    <td width="73%" valign="top"> Student interesetd for retotaling/ rechecking are to apply through online mode on or before <?php echo !empty($result[0]->end_date)?date('d.m.Y',strtotime($result[0]->end_date)):'' ?> by paying Rs 260 per answer script to the college.</td>
                </tr>
                <tr>    
                    <td width="3%">4.</td>
                    <td width="73%" valign="top"> The student intrested to apply for Sl.no 2 and or 3 above as per above dateline should deposit the dues through ONLINE mode only.</td>
                 </tr>
                 <tr>   
                    <td width="3%" valign="top">5. </td>
                    <td width="73%" valign="top"> The SGPA shown for the subjects dispalyed on this page.</td>
                </tr>
                
               <?php } ?> 
                
            </table>
        </td>
    
    </tr>
    
    <tr align="center" class="print-column">
    	<td><a href="javascript:void(0);" class="btn btn-primary" onclick="window.print()">Print</a>&nbsp;<a href="<?php echo base_url('admin/stugroup'); ?>/<?php echo md5($result[0]->id)?>" class="btn btn-default">Exit</a></td>
    
    </tr>
    
</table>



</body>
</html>



