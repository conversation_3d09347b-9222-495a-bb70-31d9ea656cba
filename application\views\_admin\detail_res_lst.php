  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        Result Set
        <!--<small>it all starts here</small>-->
      </h1>
      <ol class="breadcrumb">
        <li><a href="<?php echo base_url('admin/dashboard'); ?>"><i class="fa fa-dashboard"></i> Home</a></li>
        <li><a href="<?php echo base_url('admin/resultset'); ?>"> Result Set</a></li>
        <li class="active">Detailed Result List</li>
      </ol>
    </section>

    <!-- Main content -->
    <section class="content">

      <!-- Default box -->
      <div class="box">
     
        <div class="box-header with-border">
         <a href="<?php echo base_url('admin/importresult'); ?>" class="btn btn-sm btn-primary pull-right">Import Result</a>
        </div>
        <div class="box-body">
			<table id="tbl_result_set" class="table table-bordered table-striped">
                <thead>
                <tr>
                  <th>Regdno</th>
                  <th>Branch</th>
                  <th>Subject Code</th>
                  <th>Subject Name</th>
                  <th>Credits</th>
                  <th>Grade</th>
                  <th>Sgpa</th>
                  <th>Subject Type</th>
                </tr>
                </thead>
                <tbody>
                <?php  foreach($detailres as $res){?>
                    <tr>
                      <td><?php echo $res->regd_no; ?></td>
                      <td><?php echo $res->branch; ?></td>
                      <td><?php echo $res->subject_code; ?></td>
                      <td><?php echo $res->subject_name; ?></td>
                      <td><?php echo $res->credits; ?></td>
                      <td><?php echo $res->grade; ?></td>
                      <td><?php echo $res->sgpa; ?></td>
                      <td><?php echo $res->subject_type; ?></td>
                     	 
                    </tr>
               <?php } ?>
                
                </tbody>

              </table>

          
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
             
        </div>
        <!-- /.box-footer-->
     
      
      </div>
      <!-- /.box -->

    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
  <script>
  
  	$(document).ready(function(e) {
       $('#tbl_result_set').DataTable({
			
			'paging'      : true,
			'searching'   : true,
			'ordering'    : true,
			'info'        : true,
			'autoWidth'   : false,
			"bLengthChange": false,
			"order": []
		
		});

    });
  


	
	function deleteSet(id){
		bootbox.confirm({ 
			size: "small",
			message: "Are you sure? You want to delete this.",
			callback: function(result){ /* result is a boolean; true = OK, false = Cancel*/
					if(result){
							$.ajax({
								type: "POST",
								url: "<?php echo base_url('admin/deleteset') ?>",
								data: {'id':id},
								dataType: 'json',
								beforeSend: function (xhr) {
								  $(".btn_" + id).prop("disabled",true);
								  $(".btn_" + id).html('<i class="fa fa-spinner fa-spin"></i>');
								  
								},
								success: function (res_data) {
									  $(".btn_" + id).prop("disabled",false);
									  $(".btn_" + id).html('<i class="fa fa-trash"></i>');
									
									if (res_data[0].resSuccess == 1) { 

										
										bootbox.alert({ 
											size: "small",
											message: "Instructor deleted sucessfully.",
											callback:function(){
													window.location.href=window.location.href;
												}
													
											});	
									  
									}else if (res_data[0].resSuccess == 2){
										
											 bootbox.alert({ 
												size: "small",
												message: res_data[0].msg});	
											}					
									}
								});
						
					}
			 }
		})
		
	}	
  
  </script>
  
  
  
  