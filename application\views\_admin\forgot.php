<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <title><?php echo APP_TITLE ?> | Admin Log in</title>
  <!-- Tell the browser to be responsive to screen width -->
  <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
  <!-- Bootstrap 3.3.7 -->
  <link rel="stylesheet" href="<?php echo base_url('assets/bower_components/bootstrap/dist/css/bootstrap.min.css') ?>">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="<?php echo base_url('assets/bower_components/font-awesome/css/font-awesome.min.css') ?>">
  <!-- Ionicons -->
  <link rel="stylesheet" href="<?php echo base_url('assets/bower_components/Ionicons/css/ionicons.min.css') ?>">
  <!-- Theme style -->
  <link rel="stylesheet" href="<?php echo base_url('assets/dist/css/AdminLTE.min.css') ?>">

  <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
  <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
  <!--[if lt IE 9]>
  <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
  <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
  <![endif]-->

  <!-- Google Font -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,600,700,300italic,400italic,600italic">
<style>
body{ background:url("<?php echo base_url('assets/images/cet-bg.jpg'); ?>") !important; background-size:cover !important; background-repeat:no-repeat;}
.login-logo a { color:#fff;}
</style>
</head>
<body class="hold-transition login-page">
<div class="login-box">
  <div class="login-logo">
    <a href="<?php echo base_url('admin') ?>"><b>RESULTS<br/><span class="text-small">CET BHUBANESWAR</span></b></a>
  </div>
  <!-- /.login-logo -->
  <div class="login-box-body">
    <p class="login-box-msg"><strong>Enter your email Id </strong></p>

    <form id="forgotPwd" method="POST" novalidate>
      <div class="form-group has-feedback">
        <input type="email" id="email" name="email" autocomplete="off" class="form-control" placeholder="Email" required>
        <span class="glyphicon glyphicon-envelope form-control-feedback"></span>
      </div>
      <div class="row">
        <!-- /.col -->
        <div class="col-xs-12">
          <button type="submit" class="btn btn-primary btn-block btn-flat" id="btnForgot">Forgot password?</button>
        </div>
        <!-- /.col -->
      </div>
    </form>


  </div>
  <!-- /.login-box-body -->
</div>
<!-- /.login-box -->

<!-- jQuery 3 -->
<script src="<?php echo base_url('assets/bower_components/jquery/dist/jquery.min.js') ?>"></script>
<!-- Bootstrap 3.3.7 -->
<script src="<?php echo base_url('assets/bower_components/bootstrap/dist/js/bootstrap.min.js') ?>"></script>
<!-- Validation Plugin Js -->
<script src="<?php echo base_url('assets/plugins/jquery-validation/jquery.validate.js') ?>"></script>
<script src="<?php echo base_url('assets/plugins/bootbox/bootbox.all.min.js') ?>"></script>

<script>
    
$(function () {
//creteToast('Login Error','Unauthenciated user','error');
	$('#forgotPwd').validate({
			    rules: {
					  email: {
						required: true,
						email: true
					  },
					  
    },
    // Specify validation error messages
    messages: {
      email: "Please enter a valid email address"
    },highlight: function (input) {
			$(input).parent('.form-group').addClass('has-error');
		},
		unhighlight: function (input) {
			$(input).parent('.form-group').removeClass('has-error');
		},
		errorPlacement: function (error, element) {
			$(element).parents('.form-group').append(error);
		}
	});
	
	
	$('#forgotPwd').submit(function(e){
		e.preventDefault();
		if($('#forgotPwd').valid()) {
				$.ajax({
				type: "POST",
				url: "<?php echo base_url('admin/sendpwd') ?>",
				data: $("#forgotPwd").serialize(),
				dataType: 'json',
				beforeSend: function (xhr) {
				  $("#btnForgot").prop("disabled",true);
				  $('#btnForgot').html("<i class='fa fa-spinner fa-spin'></i> Forgot password...")
				},
				success: function (res_data) {
					  $("#btnForgot").prop("disabled",false);
					  $('#btnForgot').html("Forgot password?")
					if (res_data[0].resSuccess == 1) { 
						   bootbox.alert({ 
								size: "small",
								message: 'A reset link is sent to your registered email, please follow the link to reset your password.',
								callback:function(){
										window.location.href="<?php echo base_url('admin'); ?>";
									}
							});	

					}else if (res_data[0].resSuccess == 2){
						   bootbox.alert({ 
								size: "small",
								message: res_data[0].msg,
								callback:function(){
										//window.location.href=window.location.href;
									}
							});	
				
							return false;
						}					
					}
				});
			}
		return false;
		});
	
	});

   </script>
</html>
