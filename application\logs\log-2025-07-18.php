<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

ERROR - 2025-07-18 00:17:55 --> Query error: Column 'rechecking_type' cannot be null - Invalid query: INSERT INTO `tbl_payment_subjects` (`payment_id`, `rechecking_type`, `subject_code`, `subject_name`) VALUES (15511,NULL,'BH1401','CHEMISTRY')
ERROR - 2025-07-18 00:18:01 --> Query error: Column 'rechecking_type' cannot be null - Invalid query: INSERT INTO `tbl_payment_subjects` (`payment_id`, `rechecking_type`, `subject_code`, `subject_name`) VALUES (15512,NULL,'BH1401','CHEMISTRY')
ERROR - 2025-07-18 05:52:30 --> Query error: Column 'rechecking_type' cannot be null - Invalid query: INSERT INTO `tbl_payment_subjects` (`payment_id`, `rechecking_type`, `subject_code`, `subject_name`) VALUES (15543,NULL,'IP6002','DISASTER MANAGEMENT')
ERROR - 2025-07-18 05:52:35 --> Query error: Column 'rechecking_type' cannot be null - Invalid query: INSERT INTO `tbl_payment_subjects` (`payment_id`, `rechecking_type`, `subject_code`, `subject_name`) VALUES (15544,NULL,'IP6002','DISASTER MANAGEMENT')
ERROR - 2025-07-18 05:55:46 --> Query error: Column 'rechecking_type' cannot be null - Invalid query: INSERT INTO `tbl_payment_subjects` (`payment_id`, `rechecking_type`, `subject_code`, `subject_name`) VALUES (15546,NULL,'EE1401','BASIC ELECTRICAL ENGINEERING')
ERROR - 2025-07-18 05:55:51 --> Query error: Column 'rechecking_type' cannot be null - Invalid query: INSERT INTO `tbl_payment_subjects` (`payment_id`, `rechecking_type`, `subject_code`, `subject_name`) VALUES (15547,NULL,'EE1401','BASIC ELECTRICAL ENGINEERING')
ERROR - 2025-07-18 06:56:57 --> Query error: Column 'rechecking_type' cannot be null - Invalid query: INSERT INTO `tbl_payment_subjects` (`payment_id`, `rechecking_type`, `subject_code`, `subject_name`) VALUES (15552,NULL,'MS1403','ENGINEERING MECHANICS')
ERROR - 2025-07-18 06:57:05 --> Query error: Column 'rechecking_type' cannot be null - Invalid query: INSERT INTO `tbl_payment_subjects` (`payment_id`, `rechecking_type`, `subject_code`, `subject_name`) VALUES (15553,NULL,'MS1403','ENGINEERING MECHANICS')
ERROR - 2025-07-18 10:25:49 --> Query error: Column 'rechecking_type' cannot be null - Invalid query: INSERT INTO `tbl_payment_subjects` (`payment_id`, `rechecking_type`, `subject_code`, `subject_name`) VALUES (15576,NULL,'BT6102','GENE MANIPULATION & VECTOR TECHNOLOGY')
ERROR - 2025-07-18 10:25:54 --> Query error: Column 'rechecking_type' cannot be null - Invalid query: INSERT INTO `tbl_payment_subjects` (`payment_id`, `rechecking_type`, `subject_code`, `subject_name`) VALUES (15577,NULL,'BT6102','GENE MANIPULATION & VECTOR TECHNOLOGY')
ERROR - 2025-07-18 10:26:23 --> Query error: Column 'rechecking_type' cannot be null - Invalid query: INSERT INTO `tbl_payment_subjects` (`payment_id`, `rechecking_type`, `subject_code`, `subject_name`) VALUES (15579,NULL,'BT6102','GENE MANIPULATION & VECTOR TECHNOLOGY')
ERROR - 2025-07-18 13:49:13 --> Query error: Column 'rechecking_type' cannot be null - Invalid query: INSERT INTO `tbl_payment_subjects` (`payment_id`, `rechecking_type`, `subject_code`, `subject_name`) VALUES (15593,NULL,'BH1461','PHYSICS')
ERROR - 2025-07-18 13:49:20 --> Query error: Column 'rechecking_type' cannot be null - Invalid query: INSERT INTO `tbl_payment_subjects` (`payment_id`, `rechecking_type`, `subject_code`, `subject_name`) VALUES (15594,NULL,'BH1461','PHYSICS')
ERROR - 2025-07-18 14:59:58 --> Query error: Column 'rechecking_type' cannot be null - Invalid query: INSERT INTO `tbl_payment_subjects` (`payment_id`, `rechecking_type`, `subject_code`, `subject_name`) VALUES (15603,NULL,'BH1401','CHEMISTRY')
ERROR - 2025-07-18 17:17:59 --> Query error: Column 'rechecking_type' cannot be null - Invalid query: INSERT INTO `tbl_payment_subjects` (`payment_id`, `rechecking_type`, `subject_code`, `subject_name`) VALUES (15618,NULL,'BH6102','INORGANIC CHEMISTRY-II')
