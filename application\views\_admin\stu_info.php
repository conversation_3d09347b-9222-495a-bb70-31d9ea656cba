 <?php 
 	//echo count($success_record);
	//echo $total_record; die();
 ?>
 <style>
 	.btn-circle {
  width: 30px;
  height: 30px;
  text-align: center;
  padding: 6px 0;
  font-size: 12px;
  line-height: 1.42;
  border-radius: 15px;
}
 </style>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        Student List
        <!--<small>it all starts here</small>-->
      </h1>
      <!--<ol class="breadcrumb">
        <li><a href="#"><i class="fa fa-dashboard"></i> Home</a></li>
        <li><a href="#">Examples</a></li>
        <li class="active">Blank page</li>
      </ol>-->
    </section>

    <!-- Main content -->
    <section class="content">

      <!-- Default box -->
      <div class="box">
      <form id="frmStuInfo" method="post" novalidate>
      	<?php 
			foreach($resinfo as $key => $val){
				echo '<input type="hidden" name="' . $key.'" value="' . $val .'"/>';
			}
		?>
        
        <input type="hidden" name="filename" value="<?php echo $filename; ?>"/>
      
      
        <div class="box-body">
        	<?php if(count($success_record) == $total_record){ ?>		
        		<p class="text-green"> All <?php echo $total_record ?> Records will be imported.</p>
            <?php }else{ ?>
        		<p class="text-red"> <?php echo count($success_record) ?> out of <?php echo $total_record; ?> Records will be Imported.</p>
            <?php } ?>
           
            	<div class="col-sm-12" style="background-color:#dedede; margin-bottom:10px;">
                	
                   <div class="row"> 
                   	<table class="table" style="margin-bottom:0px;">
                    	<tr>
                        	<td width="5%"><strong>Title</strong></td>
                            <td width="1%">:</td>	
                            <td width="69%"><?php echo $resinfo['result_title']; ?></td>
                        </tr>
                        
                    	<tr>
                        	<td><strong>Stream</strong></td>
                            <td>:</td>	
                            <td><?php echo $streams[$resinfo['stream']][0]; ?></td>
                        </tr>
                        
                    	<tr>
                        	<td><strong>Semester</strong></td>
                            <td>:</td>	
                            <td><?php echo $resinfo['semester']; ?> </td>
                        </tr>
                    
                    </table>
                  </div>  
                    
                </div>
          
        
        	<ul class="nav nav-tabs">
                  <li class="active"><a data-toggle="tab" href="#success">To be Imported <span class="text-green"><?php if(!empty($success_record)){ echo '(' . count($success_record) . ')';	 } ?></span></a></li>
                 <?php if(!empty($failed_record)){ ?>
                	  <li><a data-toggle="tab" href="#failed">Will not be Imported  <span class="text-red"><?php if(!empty($failed_record)){ echo '(' . count($failed_record) . ')'; } ?></span></a></li>
				<?php } ?>
            </ul>

            <div class="tab-content">
              <div id="success" class="tab-pane fade in active">
              		<div class = "table-responsive" style="margin-top:10px;">
                   		 <table id="tbl_success_set" class="table table-bordered table-striped table-sm">
                                    <thead>
                                    <tr>
                                      <th>Branch</th>	
                                      <th>Regno</th>
                                      <th>Name</th>
                                      <th>Email</th>
                                      <th>Subcode</th>
                                      <th>Subject</th>
                                      <th>credits</th>
                                      <th>Grade</th>
                                      <th>SGPA</th>
                                      <th>Type</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <?php  $i=1; foreach($success_record as $stu){
                                        
                                         //if($i == 1){ $i++; continue;}
										 
										 ?>
                                        <tr>
                                          <td><?php echo $stu[0]; ?></td>
                                          <td><?php echo $stu[1]; ?></td>
                                          <td><?php echo $stu[2]; ?></td>
                                          <td><?php echo $stu[3]; ?></td>
                                          <td><?php echo $stu[4]; ?></td>
                                          <td><?php echo $stu[5]; ?></td>
                                          <td><?php echo $stu[6]; ?></td>
                                          <td><?php echo $stu[7]; ?></td>
                                          <td><?php echo $stu[8]; ?></td>
                                          <td><?php echo $stu[9]; ?></td>
                                        </tr>
                                   <?php } ?>
                                    
                                    </tbody>
                                    <!--<tfoot>
                                    <tr>
                                      <th>Category Name</th>
                                      <th>Task</th>
                                      
                                    </tr>
                                    </tfoot>-->
                                  </table>
                    </div>              
              </div>
             
             <?php if(!empty($failed_record)){ ?> 
             	 <div id="failed" class="tab-pane fade">
             		<div class = "table-responsive" style="margin-top:10px;">
                    	<table id="tbl_failed_set" class="table table-bordered table-striped">
                                    <thead>
                                    <tr>
                                      <th>Branch</th>	
                                      <th>Regno</th>
                                      <th>Name</th>
                                      <th>Email</th>
                                      <th>Subcode</th>
                                      <th>Subject</th>
                                      <th>credits</th>
                                      <th>Grade</th>
                                      <th>SGPA</th>
                                      <th>Type</th>
                                      <th>Reason</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <?php  $i=1; foreach($failed_record as $stu){
                                        
                                         //if($i == 1){ $i++; continue;}
										 
										 ?>
                                        <tr>
                                          <td><?php echo $stu[0]; ?></td>
                                          <td><?php echo $stu[1]; ?></td>
                                          <td><?php echo $stu[2]; ?></td>
                                          <td><?php echo $stu[3]; ?></td>
                                          <td><?php echo $stu[4]; ?></td>
                                          <td><?php echo $stu[5]; ?></td>
                                          <td><?php echo $stu[6]; ?></td>
                                          <td><?php echo $stu[7]; ?></td>
                                          <td><?php echo $stu[8]; ?></td>
                                          <td><?php echo $stu[9]; ?></td>
                                          <td><?php echo $stu[10]; ?></td>
                                        </tr>
                                   <?php } ?>
                                    
                                    </tbody>
                                    <!--<tfoot>
                                    <tr>
                                      <th>Category Name</th>
                                      <th>Task</th>
                                      
                                    </tr>
                                    </tfoot>-->
                                  </table>
                    </div>              
             
              </div>
             <?php } ?> 
              
            </div>
          
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
        
        	
            	
                <div class="col-sm-12">
                	<?php if(!empty($_POST['recid'])){?>
                   		 <a href="<?php echo base_url('admin/importresult/'. $_POST['recid']) ?>" class="btn btn-default">Exit</a>
                    <?php }else{ ?>
                   		 <a href="<?php echo base_url('admin/importresult') ?>" class="btn btn-default">Exit</a>
                    <?php } ?>
                    <button type="submit" class="btn btn-primary btn-sm pull-right" id="btnsave" <?php echo count($success_record)> 0?'':'disabled'; ?>>Confirm and Save</button>
                </div>
           
            
        </div>
        <!-- /.box-footer-->
      </form>
      
      </div>
      <!-- /.box -->

    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
  <script>
  
  	$(document).ready(function(e) {
		
		 $('#tbl_success_set').DataTable({
			
			'paging'      : true,
			'searching'   : true,
			'ordering'    : true,
			'info'        : true,
			'autoWidth'   : false,
			"bLengthChange": false,
			"order": []
		
		});
		
	  <?php if(!empty($failed_record)){ ?> 	
		 $('#tbl_failed_set').DataTable({
			
			'paging'      : true,
			'searching'   : true,
			'ordering'    : true,
			'info'        : true,
			'autoWidth'   : false,
			"bLengthChange": false,
			"order": []
		
		});
	 <?php } ?>	
		
	$('#frmStuInfo').submit(function(e) {
        
		e.preventDefault();
		
				$.ajax({
				type: "POST",
				url: "<?php echo base_url('admin/saveresultset') ?>",
				data: $("#frmStuInfo").serialize(),
				dataType: 'json',
				beforeSend: function (xhr) {
				  $("#btnsave").prop("disabled",true);
				  $("#btnsave").html('<i class="fa fa-spinner fa-spin"></i> Saving...');
				  
				},
				success: function (res_data) {
					if (res_data[0].resSuccess == 1) { 
							bootbox.alert({ 
								size: "small",
								message: "Result saved successfully",
								callback:function(){
										window.location.href="<?php echo base_url('admin/resultset') ?>";
									}
										
								});	
					}else if (res_data[0].resSuccess == 2){
						  $("#btnsave").prop("disabled",false);
						  $("#btnsave").html('Confirm and Save');
						   bootbox.alert({ 
								size: "small",
								message: res_data[0].msg,
								callback:function(){
										//window.location.href=window.location.href;
									}
							});	
				
							return false;
						}					
					}
				});
			
		return false;
		});

    });
  
  </script>