<!DOCTYPE html>
<html>

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title><?php echo APP_STUDENT_TITLE ?> | Student Forgot Password</title>
	<!-- Tell the browser to be responsive to screen width -->
	<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
	<!-- Bootstrap 3.3.7 -->
	<link rel="stylesheet" href="<?php echo base_url('assets/bower_components/bootstrap/dist/css/bootstrap.min.css') ?>">
	<!-- Font Awesome -->
	<link rel="stylesheet" href="<?php echo base_url('assets/bower_components/font-awesome/css/font-awesome.min.css') ?>">
	<!-- Ionicons -->
	<link rel="stylesheet" href="<?php echo base_url('assets/bower_components/Ionicons/css/ionicons.min.css') ?>">
	<!-- Theme style -->
	<link rel="stylesheet" href="<?php echo base_url('assets/dist/css/AdminLTE.min.css') ?>">

	<!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
	<!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
	<!--[if lt IE 9]>
  <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
  <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
  <![endif]-->

	<!-- Google Font -->
	<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,600,700,300italic,400italic,600italic">
	<style>
		body {
			background: url("<?php echo base_url('assets/images/MAIN_GATE.jpg'); ?>") !important;
			background-size: cover !important;
			background-repeat: no-repeat;
		}

		.login-logo a {
			color: #fff;
		}

		.invalid-feedback {
			color: #dc3545;
			font-size: 0.875em;
			margin-top: 0.25rem;
		}

		.is-invalid {
			border-color: #dc3545;
		}
	</style>
</head>

<body class="hold-transition login-page">
	<div class="login-box">
		<div class="login-logo">
			<a href="<?php echo base_url('/') ?>"><b>STUDENT PORTAL<br /><span class="text-small">OUTR (Formerly CET) BHUBANESWAR</span></b></a>
		</div>
		<!-- /.login-logo -->
		<div class="login-box-body">
			<p class="login-box-msg"><strong>Forgot Password</strong></p>

			<form id="forgotPwd" method="POST" novalidate>
				<div class="form-group has-feedback">
					<input type="text" id="regdno" name="regdno" autocomplete="off" class="form-control" placeholder="Registration Number*" required>
					<span class="glyphicon glyphicon-user form-control-feedback"></span>
				</div>
				<div class="row">
					<div class="col-sm-8">
						<a href="<?php echo base_url('student'); ?>">Back to Login</a>
					</div>
					<!-- /.col -->
					<div class="col-xs-4">
						<button type="submit" class="btn btn-primary btn-block btn-flat" id="btnForgot">Reset</button>
					</div>
					<!-- /.col -->
				</div>
			</form>

		</div>
		<!-- /.login-box-body -->
	</div>
	<!-- /.login-box -->

	<!-- jQuery 3 -->
	<script src="<?php echo base_url('assets/bower_components/jquery/dist/jquery.min.js') ?>"></script>
	<!-- Bootstrap 3.3.7 -->
	<script src="<?php echo base_url('assets/bower_components/bootstrap/dist/js/bootstrap.min.js') ?>"></script>
	<!-- AdminLTE App -->
	<script src="<?php echo base_url('assets/dist/js/adminlte.min.js') ?>"></script>

	<!-- jQuery Validation removed - using manual validation instead -->
	<!-- Use CDN for bootbox to avoid MIME type issues -->
	<script src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/5.5.2/bootbox.min.js"></script>

	<script>
		$(document).ready(function() {

			// Simple manual validation function
			function validateForm() {
				var regdno = $('#regdno').val().trim();
				var errorMsg = '';

				// Clear previous errors
				$('.error-message').remove();
				$('#regdno').removeClass('is-invalid');

				if (regdno === '') {
					errorMsg = 'Please enter your registration number';
				} else if (regdno.length < 5) {
					errorMsg = 'Registration number must be at least 5 characters';
				}

				if (errorMsg) {
					$('#regdno').addClass('is-invalid');
					$('#regdno').after('<span class="error-message" style="color: #dc3545; font-size: 0.875em; display: block; margin-top: 5px;">' + errorMsg + '</span>');
					return false;
				}

				return true;
			}

			$('#forgotPwd').submit(function(e) {
				e.preventDefault();

				// Validate form
				if (!validateForm()) {
					return false;
				}

				$.ajax({
					type: "POST",
					url: "<?php echo base_url('student/sendpwd') ?>",
					data: $("#forgotPwd").serialize(),
					dataType: 'json',
					beforeSend: function(xhr) {
						$("#btnForgot").prop("disabled", true);
						$('#btnForgot').html("<i class='fa fa-spinner fa-spin'></i> Sending...");
					},
					success: function(res_data) {
						$("#btnForgot").prop("disabled", false);
						$('#btnForgot').html("Reset");

						// Check if response is valid
						if (res_data && res_data[0]) {
							if (res_data[0].resSuccess == 1) {
								if (typeof bootbox !== 'undefined') {
									bootbox.alert({
										size: "small",
										message: 'A reset link has been sent to your registered email. Please check your email and follow the link to reset your password.',
										callback: function() {
											window.location.href = "<?php echo base_url('student'); ?>";
										}
									});
								} else {
									alert('A reset link has been sent to your registered email. Please check your email and follow the link to reset your password.');
									window.location.href = "<?php echo base_url('student'); ?>";
								}
							} else if (res_data[0].resSuccess == 2) {
								var errorMsg = res_data[0].msg || 'An error occurred. Please try again.';
								if (typeof bootbox !== 'undefined') {
									bootbox.alert({
										size: "small",
										message: errorMsg
									});
								} else {
									alert(errorMsg);
								}
							}
						} else {
							alert('Invalid response from server. Please try again.');
						}
					},
					error: function(xhr, status, error) {
						$("#btnForgot").prop("disabled", false);
						$('#btnForgot').html("Reset");

						console.log('AJAX Error:', error);
						console.log('Response:', xhr.responseText);

						var errorMsg = 'An error occurred while processing your request. Please try again.';
						if (xhr.responseText) {
							// Try to extract meaningful error from response
							if (xhr.responseText.includes('404')) {
								errorMsg = 'Service not found. Please contact administrator.';
							} else if (xhr.responseText.includes('500')) {
								errorMsg = 'Server error. Please try again later.';
							}
						}

						if (typeof bootbox !== 'undefined') {
							bootbox.alert({
								size: "small",
								message: errorMsg
							});
						} else {
							alert(errorMsg);
						}
					}
				});

				return false;
			});
		});
	</script>
</body>

</html>