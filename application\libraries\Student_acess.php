<?php
defined('BASEPATH') OR exit('No direct script access allowed');
class Student_acess extends CI_Controller {
	protected $CI;
	 public function __construct(){
			$this->CI =& get_instance();
			$this->CI->load->library('session');
			if (!$this->CI->session->has_userdata('stu_sess_login') &&  !$this->CI->session->userdata('stu_sess_login') == 1) {
				$this->CI->session->set_flashdata('message', 'Unauthenticated Access. Please Login !');
				redirect("/");
			}
        }
}
