<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
class Rechecking_model extends CI_Model {

    function __construct()
    {
        // Call the Model constructor
        parent::__construct();
		
    }
	

	
	
	public function getResult(){
		$regd_no = $this->session->userdata('stu_sess_user_id');
		$this->db->select('tr.id,tr.title,tr.stream,tr.semester,tr.publish_date,tr.rechecking_allowed,tr.start_date,tr.end_date,tr.result_type');
		$this->db->from('tbl_result tr');
		$this->db->where('tr.status','1');
		$this->db->where('tr.rechecking_allowed=','1');
		$this->db->where('tr.start_date<=',date('Y-m-d'));
		$this->db->where('tr.end_date>=',date('Y-m-d'));
		//$this->db->where('tr.start_date<=','2021-03-26');
		//$this->db->where('tr.end_date>=','2021-03-26');
		$this->db->where("EXISTS(SELECT regd_no FROM tbl_result_details trd where trd.result_id = tr.id  and trd.regd_no = " . $regd_no.")");
		$query = $this->db->get();
		//echo $this->db->last_query();
		return $query->result();
	}
	
	public function getSubjectByResultId($result_id){ 
		$regd_no = $this->session->userdata('stu_sess_user_id');
		$this->db->select('trd.result_id,trd.regd_no,trd.branch,trd.subject_code,trd.subject_name,trd.subject_type');
		$this->db->from('tbl_result_details trd');				
		$this->db->where(array('trd.regd_no' => $regd_no, 'trd.subject_type' => 'T', 'md5(trd.result_id)' => $result_id));
		//$this->db->where(array('trd.regd_no' => $regd_no, 'trd.subject_type' => 'P', 'md5(trd.result_id)' => $result_id));
		$query = $this->db->get();	
		//echo $this->db->last_query();
		return $query->result();		
	
	}
	
	
	public function getResultById($result_id){
		$this->db->select('id,title,stream,semester,publish_date,rechecking_allowed,start_date,end_date,result_type');
		$this->db->from('tbl_result');
		$this->db->where(array('md5(id)' => $result_id));
		$query = $this->db->get();
		//echo $this->db->last_query(); die('fff');
		return $query->row();
		
	}
	
	
	
	public function getStudentResult($result_id){
		$regd_no = $this->session->userdata('stu_sess_user_id');
		$this->db->select('tr.id,tr.title,tr.stream,tr.semester,tr.publish_date,tr.rechecking_allowed,tr.start_date,tr.end_date,tr.result_type,
						   trd.branch,trd.subject_code,trd.subject_name,trd.credits,trd.grade,trd.sgpa,trd.subject_type');
		$this->db->from('tbl_result tr');
		$this->db->join('tbl_result_details trd', 'tr.id = trd.result_id');
		$this->db->where(array('trd.regd_no' => $regd_no, 'md5(trd.result_id)' => $result_id));
		$query = $this->db->get();
		//echo $this->db->last_query(); die('xxx');
		return $query->result();
		
	}
	
	
	
	public function getSubjectBySubCode($sub_code, $regd_no){
		
		$this->db->select('subject_name');
		$this->db->from('tbl_result_details');
		$this->db->where('subject_code' , $sub_code);
		$this->db->where('regd_no' , $regd_no);
		$this->db->limit(1);
		$query = $this->db->get();
		return $query->row();
	}
	
	
	
	
	public function insertPayment($data){
		
		$this->db->insert('tbl_payments', $data);
		$insert_id = $this->db->insert_id();
		return ($this->db->affected_rows() != 1) ? false : $insert_id;

		
	}
	
	
	
	public function updatePayment($data,$pay_id){
		$this->db->where(array('id' => $pay_id));
		$this->db->update('tbl_payments', $data);
		//echo $this->db->last_query(); die();
		if($this->db->affected_rows() >=0){
			return true;	
		}else{
			return false;
		}
		
	}
	
	
	public function insertPaymentSubjects($data){
		$this->db->insert_batch('tbl_payment_subjects',$data);		
	}
	
	
	public function  getPaymentInformation(){
		$regd_no = $this->session->userdata('stu_sess_user_id');
		$this->db->select('tp.id,tp.regd_no,tp.result_id,tp.payment_date,tp.payment_amount,tp.payment_status,tp.tnx_reference_no,
						   tp.manually_approved,tp.manual_approved_date,tp.Remarks,tr.title,tr.stream,tr.semester');
		$this->db->from('tbl_payments tp');
		$this->db->join('tbl_result tr','tr.id=tp.result_id');
		$this->db->where('tp.regd_no' , $regd_no);
		$query = $this->db->get();
		return $query->result();
			
	}
	
	
	
	public function getSubjectsBypayId($payment_id){
		$this->db->select('tps.subject_code,tps.subject_name,tps.rechecking_type');
		$this->db->from('tbl_payment_subjects tps');
		$this->db->where(array('payment_id' => $payment_id));
		$query = $this->db->get();
		return $query->result();
	}
	
	public function getSubjectsByPaymentDetails($payment_id){
		
		$this->db->select('tp.id,tp.regd_no,tp.result_id,DATE_FORMAT(tp.payment_date, "%d-%m-%Y") AS payment_date,tp.payment_amount,tp.payment_status,tp.tnx_reference_no,tps.subject_code,tps.subject_name,tps.rechecking_type, tr.title,tr.stream,tr.semester,ts.name');
		$this->db->from('tbl_payments tp');
		$this->db->join('tbl_payment_subjects tps','tps.payment_id=tp.id');				
		$this->db->join('tbl_result tr','tr.id=tp.result_id');
		$this->db->join('tbl_students ts','ts.regd_no=tp.regd_no');				
		$this->db->where(array('md5(tp.id)' => $payment_id, 'tp.payment_status' => 1));
		$query = $this->db->get();
		//echo $this->db->last_query(); die();		
		return $query->result();				
		
	}
			
	//-----------------------------------------------------
	
	public function getAdmindetails($email=''){
		$this->db->select('password');
		$this->db->from('tbl_admin');
		$this->db->where(array('id' => $this->session->userdata('sess_user_id')));
        $query = $this->db->get();
		return $query->row();		
	}
	
	
	
	public function updateProfile($data){ 
		$id = $this->session->userdata('sess_user_id');
		$this->db->where(array('id' => $id));
		$this->db->update('tbl_admin', $data);
		//echo $this->db->last_query(); die();
		if($this->db->affected_rows() >=0){
			return true;	
		}else{
			return false;
		}

		
	}
	
	
	
	
	
	public function deleteFiles($path){
		$file = $path; 
		if(is_file($file)){
			unlink($file); 
		}
		return 1;   
	}
	
	public function getAdminUserDetails($adminemail){
		$this->db->order_by("ID", "ASC");
		$this->db->where(array('UserEmail' => $adminemail, 'Status' => 1));
        $query = $this->db->get("tbl_admin",1);
        return $query->row();
	}
	
	public function getAdminUserDetailsByEmail($adminemail){
		$this->db->select('UserName, UserEmail');
		$this->db->from('tbl_admin');
		$this->db->where(array('UserEmail' => $adminemail, 'Status' => 1));		
		$query = $this->db->get();		
		return $query->row();
	}
	
	
	public function getRegUsers(){
		$this->db->select('user_id, user_name, user_email, user_status');
		$this->db->from('tbl_users');
		$query = $this->db->get();		
		return $query->result();
		
	}
	
	public function getStatusById($userid){
		
		$this->db->select('user_status');
		$this->db->from('tbl_users');
		$this->db->where(array('user_id' => $userid));		
		$query = $this->db->get();		
		return $query->row();
		
	}
	
	public function updateStatusByID($userid,$status){
		$data = array(
			'user_status' => $status,
		);
		$this->db->where(array('user_id' => $userid));
		$this->db->update('tbl_users', $data);
		//echo $this->db->last_query(); die();
		if($this->db->affected_rows() >=0){
			return true;	
		}else{
			return false;
		}

	}
	
	
	
	public function addcategory($data){
		
		$this->db->insert('tbl_category', $data);
		$insert_id = $this->db->insert_id();
		return ($this->db->affected_rows() != 1) ? false : $insert_id;

	}
	
	public function updateCategory($data,$cat_id){
		$this->db->where(array('cat_id' => $cat_id));
		$this->db->update('tbl_category', $data);
		//echo $this->db->last_query(); die();
		if($this->db->affected_rows() >=0){
			return true;	
		}else{
			return false;
		}
		
	}
	
}