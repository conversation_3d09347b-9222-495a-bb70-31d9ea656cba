  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        Result Set
        <!--<small>it all starts here</small>-->
      </h1>
      <ol class="breadcrumb">
        <li><a href="<?php echo base_url('admin/dashboard'); ?>"><i class="fa fa-dashboard"></i> Home</a></li>
        <li class="active">Result Set</li>
      </ol>
    </section>

    <!-- Main content -->
    <section class="content">

      <!-- Default box -->
      <div class="box">
      <form id="frmChangpwd" method="post" novalidate>
        <div class="box-header with-border">
         <a href="<?php echo base_url('admin/importresult'); ?>" class="btn btn-sm btn-primary pull-right">Import Result</a>
        </div>
        <div class="box-body">
			<table id="tbl_result_set" class="table table-bordered table-striped">
                <thead>
                <tr>
                  <th charset="utf-8">Result Title</th>
                  <th data-orderable="false" style="text-align:center;">Task</th>
                </tr>
                </thead>
                <tbody>
                <?php  foreach($results as $res){?>
                    <tr>
                      <td><?php echo $res->title; ?></td>
                     	 <td align="center"> 
                            <a href="<?php echo base_url('admin/detailresult') ?>/<?php echo $res->id ?>" class="btn btn-sm btn-warning">
                                <i class="fa fa-eye"></i> 
                            </a>
                            
                            <a href="<?php echo base_url('admin/stugroup') ?>/<?php echo md5($res->id) ?>" title="View set grouped by Regdno" class="btn btn-sm btn-success">
                                <i class="fa fa-users"></i> 
                            </a>
                            
                            <a href="javascript:void(0);" onclick="deleteSet(<?php echo $res->id ?>);" title="Delete Result Set" class="btn btn-sm btn-danger btn_<?php echo $res->id ?>">
                                <i class="fa fa-trash"></i> 
                            </a>
                            
						</td>
                    </tr>
               <?php } ?>
                
                </tbody>

              </table>

          
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
             
        </div>
        <!-- /.box-footer-->
      </form>
      
      </div>
      <!-- /.box -->

    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
  <script>
  
  	$(document).ready(function(e) {
       $('#tbl_result_set').DataTable({
			"columnDefs": [
				 { "width": "80%", "targets": 0 },
				 { "width": "20%",  "targets": 1 }
			],
			'paging'      : true,
			'searching'   : true,
			'ordering'    : true,
			'info'        : true,
			'autoWidth'   : false,
			"bLengthChange": false,
			"order": []
		
		});

    });
  


	
	function deleteSet(id){
		bootbox.confirm({ 
			size: "small",
			message: "Are you sure? You want to delete this.",
			callback: function(result){ /* result is a boolean; true = OK, false = Cancel*/
					if(result){
							$.ajax({
								type: "POST",
								url: "<?php echo base_url('admin/deleteset') ?>",
								data: {'id':id},
								dataType: 'json',
								beforeSend: function (xhr) {
								  $(".btn_" + id).prop("disabled",true);
								  $(".btn_" + id).html('<i class="fa fa-spinner fa-spin"></i>');
								  
								},
								success: function (res_data) {
									  $(".btn_" + id).prop("disabled",false);
									  $(".btn_" + id).html('<i class="fa fa-trash"></i>');
									
									if (res_data[0].resSuccess == 1) { 

										
										bootbox.alert({ 
											size: "small",
											message: "Instructor deleted sucessfully.",
											callback:function(){
													window.location.href=window.location.href;
												}
													
											});	
									  
									}else if (res_data[0].resSuccess == 2){
										
											 bootbox.alert({ 
												size: "small",
												message: res_data[0].msg});	
											}					
									}
								});
						
					}
			 }
		})
		
	}	
  
  </script>
  
  
  
  