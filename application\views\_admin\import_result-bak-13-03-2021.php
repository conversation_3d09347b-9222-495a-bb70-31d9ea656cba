  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        <?php echo !empty($res_id)?'Update/Re-Import Excel':'Import Result'?>
        <!--<small>it all starts here</small>-->
      </h1>
     <ol class="breadcrumb">
        <li><a href="<?php echo base_url('admin/dashboard') ?>"><i class="fa fa-dashboard"></i> Dashboard</a></li>
        <li class="active"><?php echo !empty($res_id)?'Update/Re-Import Excel':'Import Result'?></li>
      </ol>
    </section>

    <!-- Main content -->
    <section class="content">

      <!-- Default box -->
      <div class="box">
     
      <form role="form" id="importExcel" name="importExcel" action="<?php echo base_url('admin/savetempresult'); ?>" method="post" enctype="multipart/form-data">

        
        <div class="box-body">
            <div class="col-sm-6">
            
                 <div class="form-group">
                      <label for="result_title">Result Title</label>
                      <input type="text" class="form-control" id="result_title" autocomplete="off" name="result_title" value="<?php echo !empty($results->title)?$results->title:''?>"  placeholder="Result Title" required="required">
                 </div>
                 
                 
                 
                 <div class="row">
                 
                 	<div class="col-sm-6">
                         <div class="form-group">
                              <label for="stream">Select Stream</label>
                              <select id="stream" name="stream" class="form-control stream" required>
                              		<option value="">--Select Stream -- </option>
                              	<?php  foreach($streams as $key=>$stream) { ?>	
                                	<option value="<?php  echo $key ?>" <?php echo !empty($results->stream) && $results->stream == $stream[0] ?'selected':''?>><?php  echo $stream[0] ?></option>	
                              	<?php } ?>
                              </select>	
                         </div>
                    </div> 
                    
                    <div class="col-sm-6"> 
                         <div class="form-group">
                              <label for="stream">Select Semester</label>
                              <select id="semester" name="semester" class="form-control semester" required>
                             	 <option value="">--Select Semester -- </option>
                              </select>	
                         </div>
                     </div>
                 </div>  
                 
                 
              <div class="row">
                 <div class="col-sm-6">
                     <div class="form-group">
                          <label for="publish_date">Publish Date</label>
                          <input type="text" class="form-control" id="publish_date" autocomplete="off" name="publish_date" value="<?php echo !empty($results->publish_date)?date('d/m/Y',strtotime($results->publish_date)):''?>" placeholder="Publish Date" required="required">
                     </div>
                 </div>
                 
                 <div class="col-sm-6">
                 	<div class="form-group">
                      <label for="stream">Result Type</label>
                 	  <select id="result_type" name="result_type" class="form-control semester" required>
                      	<option value="">Select Result Type</option>
                      	<option value="1" <?php echo !empty($results->result_type) && $results->result_type == 1 ?'selected':''?>>Generic Result</option>
                        <option value="2" <?php echo !empty($results->result_type) && $results->result_type == 2 ?'selected':''?>>Rechecking result</option>	
                 	  </select>	
                 </div>
                 </div>
              </div>   
              
             
             <div class="row" style="border: 1px dashed #cccc;"> 
             
                    <div class="col-sm-12">
                      <!-- radio -->
                      <div class="form-group">
                        <label>Rechecking/Retotalling allowed</label><br/>
                        <label>
                          <input type="radio" name="rechecking_allowed" value="1" class="" <?php echo !empty($results->rechecking_allowed) && $results->rechecking_allowed == '1' ?'checked':''?> > Yes &nbsp;
                        </label>
                        <label>
                          <input type="radio" name="rechecking_allowed" value="0" class="" <?php echo isset($results->rechecking_allowed) && $results->rechecking_allowed == '0' ?'checked':''?>  <?php echo empty($res_id)?'checked':'';?>> No
                         
                        </label>
                        
                      </div>  
                    </div>
                    
                    <div class="col-sm-6">  
                    <div class="form-group">
                      <label for="start_date">Start Date</label>
                      <input type="text" class="form-control"  <?php echo !empty($results->rechecking_allowed) && $results->rechecking_allowed == '1' ?'':'disabled="disabled"'?> id="start_date" name="start_date" autocomplete="off"   value="<?php echo !empty($results->start_date)?date('d/m/Y',strtotime($results->start_date)):''?>" placeholder="Start Date">
                    </div>
                    </div>  
                    <div class="col-sm-6">  
                    <div class="form-group">
                      <label for="end_date">End Date</label>
                      <input type="text" class="form-control" <?php echo !empty($results->rechecking_allowed) && $results->rechecking_allowed == '1' ?'':'disabled="disabled"'?> id="end_date" name="end_date" autocomplete="off"  value="<?php echo !empty($results->end_date)?date('d/m/Y',strtotime($results->end_date)):''?>" placeholder="End Date">
                    </div>
                    </div>  
              
             </div>
              
            </div> 
            
            <div class="col-sm-6">
                    <div class="col-sm-12">	
                     <div class="form-group">
                         <label for="importresult"><?php if(!empty($res_id)){ echo 'Re Import Result Excel if required'; }else{ echo 'Import Result Excel'; }?> <small>(File Type: xlsx)</small> </label>
                         <input type="file" name="importresult" id="importresult" class="dropify" data-allowed-file-extensions="xlsx" data-max-file-size="2M" data-default-file="" data-height="170" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"/>
                    </div>	
                    </div>
           
           
           		<?php if(!empty($res_id)){?>
                    <div class="col-sm-12" style="margin-top:10px;">
                    
                            <div class="form-group">
                                <label>
                                  <input name="auto_delete" id="auto_delete" value="1" type="checkbox" class="minimal11" checked>
                                  <input type="hidden" id="hdn_auto_delete" name="hdn_auto_delete" value="1"/>
                                  Auto-delete and Re-import all rows
                                </label>
                           </div>
                                
                            <div class="form-group">
                                <label>
                                  <input name="import_only" id="import_only" value="1" type="checkbox" class="minimal11">
                                  <input type="hidden" id="hdn_import_only" name="hdn_import_only" value="0"/>
                                  Import only rows where a Student Regdno is not present
                                </label>
                           </div>
                        
                    </div>
                <?php } ?>    
           
            </div>
            
            
          
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
        	<div class="col-sm-12">
        		<a href="<?php echo base_url('admin/importresult') ?>" class="btn btn-default">Exit</a>
                <button type="submit" class="btn btn-primary btn-sm pull-right" id="btnsave"> <?php echo !empty($res_id)?'Update/Re-Import Excel':'Import Result'?></button>
       		</div>
        </div>
        	
        <input type="hidden" id="task" name="task" value="<?php echo !empty($results->id)?2:1?>"/>
        <input type="hidden" id="recid" name="recid" value="<?php echo !empty($results->id)?$results->id:0?>"/>
        
        <!-- /.box-footer-->
      </form>
      
      </div>
      <!-- /.box -->

    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
  <!-- Validation Plugin Js -->
<script src="<?php echo base_url('assets/plugins/jquery-validation/jquery.validate.js') ?>"></script>
<script src="<?php echo base_url('assets/plugins/jquery-fileupload/jquery.form.js') ?>"></script>

<script>
	var stream = <?php echo json_encode($streams) ?>;
	$(document).ready(function(e) {
		
		
		$('#auto_delete').click(function(e) {
			
			$('#import_only').prop('checked',false);
			$('#hdn_import_only').val('0');
			
           if($(this).is(':checked')){
			    $('#hdn_auto_delete').val('1');
		   }else{
			    $('#hdn_auto_delete').val('0');
		   }
		  
        });
		
		
		$('#import_only').click(function(e) {
			
			$('#auto_delete').prop('checked',false);
			$('#hdn_auto_delete').val('0');
			
           if($(this).is(':checked')){
			    $('#hdn_import_only').val('1');
		   }else{
			    $('#hdn_import_only').val('0');
		   }
		  
        });
		
		
		
		
		
		
		
		
		$('#stream').change(function(e) {
            var st = $(this).val();
			var maxsem = $(this).attr('data-maxsem'); 
			var options_val = '';
			if(st!=''){
				
				var maxsem = stream[st][1];
				maxsem = parseInt(maxsem);
				var sem = '<?php echo !empty($results->semester)?$results->semester:''?>';
				for(var i=1;i<=maxsem;i++){
					
					if(sem == i){
						options_val+='<option value="' + i +'" selected>' +  i +'</option>'
					}else{
						options_val+='<option value="' + i +'">' +  i +'</option>'
					}
				}
				
				$('#semester').html(options_val);
				
			}else{
				$('#semester').html('<option value="">--Select Semester--</option>');
				
			}
        });
		
		
		
		<?php if(!empty($results->stream)){ ?>
				$('#stream').trigger('change');
		<?php } ?>

		
		
		$("input[name='rechecking_allowed']").change(function(){
   				
				if($(this).val() == 1){
					$('#start_date').prop('disabled',false);
					$('#end_date').prop('disabled',false);	
				}else{
					$('#start_date').val('');
					$('#end_date').val('');
					$('#start_date').prop('disabled',true);
					$('#end_date').prop('disabled',true);	
				}
				
		});
		
				
		
		
			$('#importExcel').validate({
				//rules: { importresult: { required: true, extension: "xlsx", filesize: 1048576  }},
				highlight: function (input) {
					$(input).parent('.form-group').addClass('has-error');
				},
				unhighlight: function (input) {
					$(input).parent('.form-group').removeClass('has-error');
				},
				errorPlacement: function (error, element) {
					//$(element).parents('.form-group').append(error);
				},submitHandler: function(form) { 
				
					var error = 0;
					var task = $('#task').val();
					
						if($('input[name="rechecking_allowed"]').val() == 1){ 
							
								var publish_date = $('#publish_date').val();
								var start_date = $('#start_date').val();
								var end_date = $('#end_date').val();
								
								/*if((new Date(start_date) < new Date(publish_date))){ alert('ss');
										error = 1; 
										bootbox.alert({ 
											size: "small",
											message: 'Rechecking start date cannot be less or same as Result publish date .',
											callback:function(){
											//window.location.href=window.location.href;
											}
										});	
								}else if((new Date(start_date) < new Date(end_date)) && (new Date(start_date) == new Date(end_date))){ alert('xxss');
								 
										error = 1; 
										bootbox.alert({ 
											size: "small",
											message: 'Rechecking start date and end date cannot be same.',
											callback:function(){
											//window.location.href=window.location.href;
											}
										});	
								    
								}*/
						}					
					
					
					
					
					
					
					
					if(task == 1){
						
						if($('#importresult').val()==''){
							error = 1;
							bootbox.alert({ 
									size: "small",
									message: 'Please select an xlsx file to upload.',
									callback:function(){
											//window.location.href=window.location.href;
										}
								});	
						}
					}
					
					if($('#importresult').val()!=''){
						var fileExtension = ['xlsx'];
						if ($.inArray($('#importresult').val().split('.').pop().toLowerCase(), fileExtension) == -1) {
								error =1;
							bootbox.alert({ 
									size: "small",
									message: 'Invalid file format only xlsx is allowed.',
									callback:function(){
											//window.location.href=window.location.href;
										}
								});	
						}
					}
					
						if(error!=1){
							if(task == 1){
						 		form.submit();
							}else{
								
								if($('#importresult').val()!=''){
									form.submit();
								}else{
									updateResult();
								}
									
							}
						}
				 }
			});
			
			
			
			//$('#importExcel').submit();
			
			
			
			
			
			
				
				
				
/*		$('#importExcel').ajaxForm({
			target: '',
			dataType:'json',
			beforeSerialize: function($form, options) { 
			},
			beforeSubmit: function(){
				if($('#importExcel').valid()){
					  $("#btnsave").prop("disabled",true);
					  $("#btnsave").html('<i class="fa fa-spinner fa-spin"></i> Saving...');
				}						
			},
			success: function(res_data){
				$("#cmdSubmit").prop("disabled", false);	
				if(res_data[0].resSuccess == 1){
					
			
				}
				else
				{ 
					$("#cmdSubmit").prop("disabled", false);	
					
					return false;
				}
		   }	
	   });*/			
			
    });
	
	
	
	
	
	function updateResult(){
		$.ajax({
			type: "POST",
			url: "<?php echo base_url('admin/updateResult') ?>",
			data: $("#importExcel").serialize(),
			dataType: 'json',
			beforeSend: function (xhr) {
			  $("#btnsave").prop("disabled",true);
			  $("#btnsave").html('<i class="fa fa-spinner fa-spin"></i> Saving...');
			  
			},
			success: function (res_data) {
				if (res_data[0].resSuccess == 1) { 
						bootbox.alert({ 
							size: "small",
							message: "Result saved successfully",
							callback:function(){
									//window.location.href=window.location.href;
								}
									
							});	
				}else if (res_data[0].resSuccess == 2){
					  $("#btnsave").prop("disabled",false);
					   $("#btnsave").html('Update/Re-Import excel');
					   bootbox.alert({ 
							size: "small",
							message: res_data[0].msg,
							callback:function(){
									//window.location.href=window.location.href;
								}
						});	
			
						return false;
					}					
				}
			});
	}
	
	
	
	
</script>  
  
  