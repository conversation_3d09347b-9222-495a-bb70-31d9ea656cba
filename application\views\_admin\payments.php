  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        Payment Information
        <!--<small>it all starts here</small>-->
      </h1>
      <ol class="breadcrumb">
        <li><a href="<?php echo base_url('admin/dashboard'); ?>"><i class="fa fa-dashboard"></i> Home</a></li>
        <li><a href="<?php echo base_url('admin/rechecking'); ?>"><i class="fa fa-list-alt"></i> Rechecking/Retotaling</a></li>
        <li class="active">Payments</li>
      </ol>
    </section>

    <!-- Main content -->
    <section class="content">

      <!-- Default box -->
      <div class="box">
        <div class="box-body">

            <div class="col-sm-12" style="background-color:#dedede; margin-bottom:10px;">
                                        
                       <div class="row"> 
                        <table width="49%" class="table" style="margin-bottom:0px;">
                            <tbody><tr>
                                <td width="11%"><strong>Title</strong></td>
                                <td width="1%">:</td>	
                                <td width="46%"><?php echo $result->title ?></td>
                                <td width="42%">
                                	<a href="<?php echo base_url('admin/exportpaymentfailed') ?>/<?php echo $id ?>" class="btn btn-sm btn-danger pull-right" style="margin-left:5px;"><i class="fa fa-file-excel-o" aria-hidden="true"></i>&nbsp;Export Failed</a>
                                    
                                	<a href="<?php echo base_url('admin/exportpaymentpaid') ?>/<?php echo $id ?>" class="btn btn-sm btn-success pull-right" style="margin-left:5px;"><i class="fa fa-file-excel-o" aria-hidden="true"></i>&nbsp;Export Paid</a>
                                    
									<a href="<?php echo base_url('admin/exportpaymentsubjects') ?>/<?php echo $id ?>" class="btn btn-sm btn-info pull-right" ><i class="fa fa-file-excel-o" aria-hidden="true"></i>&nbsp;Export Data</a>  
                                    
                                </td>
                            </tr>
                            
                            <tr>
                                <td><strong>Stream</strong></td>
                                <td>:</td>	
                                <td><?php echo $result->stream ?></td>
                                <td></td>
                            </tr>
                            
                            <tr>
                                <td><strong>Semester</strong></td>
                                <td>:</td>	
                                <td><?php if($result->semester==11 || $result->semester==12){echo ($result->semester==11)?"Special Exam":"Back Paper";}else{ echo $result->semester;} ?></td>
                                <td></td>
                            </tr>
                        
                        </tbody></table>
                      </div>  
                        
                    </div>

			 <br/> 
        
        
        	<ul class="nav nav-tabs">
                  <li class="active"><a class="text-green" data-toggle="tab" href="#paid">Paid (<?php echo count($payments); ?>)</a></li>
                  <li><a class="text-danger" data-toggle="tab" href="#failed" >Failed (<?php echo count($failedpayments)+count($failedpayments2); ?>)</a></li>
                  
			</ul>

            <div class="tab-content">
              <div id="paid" class="tab-pane fade in active">
              
              
                    <div class="col-xs-12 table-responsive" style="margin-top:10px;" >
                         <table id="tbl_success_result_set" class="table table-bordered table-striped">
                                        <thead>
                                        <tr>
                                          <th charset="utf-8">Regno</th>
                                          <th> Student Name</th>
                                          <th> Date of submission</th>
                                          <th> Trn No</th>
                                          <th> Amount</th>
                                          <th> Status</th>
                                          <th> Last Updated</th>
                                          <th data-orderable="false"> Action</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <?php  foreach($payments as $pay){?>
                                            <tr>
                                              <td><?php echo $pay->regd_no; ?></td>
                                              <td><?php echo $pay->name; ?></td>
                                              <td><?php echo date('d-m-Y',strtotime($pay->payment_date)); ?></td>
                                              <td><?php echo $pay->txn_no; ?></td>
                                              <td><i class="fa fa-inr"></i> <?php echo $pay->payment_amount; ?></td>
                                              <td class="text-green">
											  	<?php echo ($pay->payment_status == 1)?'Success':'Failed'; ?>
											  	<?php if($pay->payment_status == 1){ ?><br><small><?php echo $pay->tnx_reference_no?></small> <?php }?>
                                              </td>
                                              <td><?php echo date('d-m-Y H:i:s',strtotime($pay->updated_at)); ?></td>
                                              <td>
                                              	<a class="btn btn-sm btn-primary" title="View list of subject" href="<?php echo base_url('admin/paymentdetails') ?>/<?php echo $pay->id ?>"><i class="fa fa-eye"></i></a>
                                              </td>
                                            </tr>
                                       <?php } ?>
                                        
                                        </tbody>
                        
                                      </table>
                     </div>             
             
              </div>
              <div id="failed" class="tab-pane fade">
              
              		<div class="col-xs-12 table-responsive" style="margin-top:10px;">
                         <table id="tbl_failed_result_set" class="table table-bordered table-striped">
                                        <thead>
                                        <tr>
                                          <th charset="utf-8">Regno</th>
                                          <th> Student Name</th>
                                          <th> Date of submission</th>
                                          <th> Trn No</th>
                                          <th> Amount</th>
                                          <th> Status / Error Msg</th>
                                          <th> Last Updated</th>
                                          <th data-orderable="false">Action</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <?php  foreach($failedpayments as $fpay){?>
                                            <tr>
                                              <td><?php echo $fpay->regd_no; ?></td>
                                              <td><?php echo $fpay->name; ?></td>
                                              <td><?php echo date('d-m-Y',strtotime($fpay->payment_date)); ?></td>
                                              <td><?php echo $fpay->txn_no; ?></td>
                                              <td><i class="fa fa-inr"></i> <?php echo $fpay->payment_amount; ?></td>
                                              <td class="text-green">
											  	<?php echo ($fpay->payment_status == 1)?'<i class="fa fa-check" aria-hidden="true"></i>':($fpay->payment_status == 0)?'Pending':'Failed'; ?>
											  		<?php #echo ($pay->payment_status == 1)?'Success':($pay->payment_status == 0)?'Pending':'Failed'; ?>
											  		<?php if($fpay->payment_status != 1){ ?><br><small><?php echo $fpay->error_message?></small> <?php }?>
                                              </td>
                                              <td><?php echo date('d-m-Y H:i:s',strtotime($fpay->updated_at)); ?></td>
                                              <td>	
                                                <a href="<?php echo base_url('admin/paymentdetails') ?>/<?php echo $fpay->id ?>" title="View list of subject" class="btn btn-sm btn-primary"><i class="fa fa-eye"></i></a>
                                             	<a href="javascript:void(0);" class="btn btn-sm btn-danger" title="Mark this payment as paid"  onclick="markaspaid(<?php echo $fpay->id ?>)">Mask as Paid</a>
                                              </td>
                                            </tr>
                                       <?php } ?>
                                        <?php  foreach($failedpayments2 as $fpay){?>
                                            <tr>
                                              <td><?php echo $fpay->regd_no; ?></td>
                                              <td><?php echo $fpay->name; ?></td>
                                              <td><?php echo date('d-m-Y',strtotime($fpay->payment_date)); ?></td>
                                              <td><?php echo $fpay->txn_no; ?></td>
                                              <td><i class="fa fa-inr"></i> <?php echo $fpay->payment_amount; ?></td>
                                              <td class="text-green">
											  	<?php echo ($fpay->payment_status == 1)?'<i class="fa fa-check" aria-hidden="true"></i>':($fpay->payment_status == 0)?'Pending':'Failed'; ?>
											  	<?php if($fpay->payment_status != 1){ ?><br><small><?php echo $fpay->error_message?></small> <?php }?>
                                              </td>
                                              <td><?php echo date('d-m-Y H:i:s',strtotime($fpay->updated_at)); ?></td>
                                              <td>	
                                                <a href="<?php echo base_url('admin/paymentdetails') ?>/<?php echo $fpay->id ?>" title="View list of subject" class="btn btn-sm btn-primary"><i class="fa fa-eye"></i></a>
                                             	<a href="javascript:void(0);" class="btn btn-sm btn-danger" title="Mark this payment as paid"  onclick="markaspaid(<?php echo $fpay->id ?>)">Mask as Paid</a>
                                             	 <?php if($fpay->payment_status == 2){?> 
                                                <a href="javascript:void(0);" class="btn btn-sm btn-info" title="Mark this as Pending"  onclick="markaspending(<?php echo $fpay->id ?>)"><i class="fa fa-gear"></i></a>
                                                <?php }?>      
                                              </td>
                                            </tr>
                                       <?php } ?>
                                        
                                        </tbody>
                        
                                      </table>
              		</div>
              </div>
            </div>
        
        
            
          
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
             
        </div>
        <!-- /.box-footer-->
      </div>
      <!-- /.box -->

    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
  
 	 <div class="modal fade" id="markaspaid-modal">
     <form id="Frmremarks" method="POST" novalidate>
          <div class="modal-dialog">
            <div class="modal-content">
              <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">Mark as paid</h4>
              </div>
              <div class="modal-body" id="pay">
               
                  <div class="form-group has-feedback">
                    <input type="text" id="tnx_ref_no" name="tnx_ref_no" maxlength="100" autocomplete="off" class="form-control" placeholder="Transaction Reference Number*" required>
                    <span class="glyphicon glyphicon-random form-control-feedback"></span>
                  </div>
                  
                  <div class="form-group has-feedback">
                    <input type="text" id="tnx_amount" name="tnx_amount" maxlength="100" autocomplete="off" class="form-control" placeholder="Transaction Amount*" required>
                    <span class="glyphicon glyphicon-random form-control-feedback"></span>
                  </div>
               
                  <div class="form-group has-feedback">
                    <textarea class="form-control" id="remarks" name="remarks" required="required" placeholder="Remarks*"></textarea>
                    <span class="glyphicon glyphicon-pencil form-control-feedback"></span>
                  </div>
               
              </div>
              
              <div class="modal-footer">
               
               <div class="row"> 
                <div class="col-sm-8">
                	<button type="button" class="btn btn-default pull-left" data-dismiss="modal">Close</button>
                </div>
                <!-- /.col -->
                <div class="col-xs-4">
                  <input type="hidden" id="pay_id" name="pay_id" value=""/>
                  <button type="submit" class="btn btn-primary pull-rigt" id="btn_paid">Confirm and Mark as paid</button>
                </div>
               </div> 

              </div>
            </div>
            <!-- /.modal-content -->
          </div>
          <!-- /.modal-dialog -->
      </form> 
    </div>
    <!-- /.modal -->
<script src="<?php echo base_url('assets/plugins/jquery-validation/jquery.validate.js') ?>"></script>
<script src="<?php echo base_url('assets/plugins/bootbox/bootbox.all.min.js') ?>"></script>

  <script>

  
  	$(document).ready(function(e) {
     
	   $('#tbl_success_result_set').DataTable({
			"columnDefs": [
				 { "width": "10%", "targets": 0 },
				 { "width": "30%",  "targets": 1 },
				 { "width": "20%",  "targets": 2 },
				 { "width": "10%",  "targets": 3 },
				 
			],
			'paging'      : true,
			'searching'   : true,
			'ordering'    : true,
			'info'        : true,
			'autoWidth'   : false,
			"bLengthChange": false,
			"order": []
		
		});
		
		
	   $('#tbl_failed_result_set').DataTable({
			"columnDefs": [
				 { "width": "10%", "targets": 0 },
				 { "width": "30%",  "targets": 1 },
				 { "width": "20%",  "targets": 2 },
				 { "width": "10%",  "targets": 3 },
				 
			],
			'paging'      : true,
			'searching'   : true,
			'ordering'    : true,
			'info'        : true,
			'autoWidth'   : false,
			"bLengthChange": false,
			"order": []
		
		});
		
		
		
		
	
	$('#Frmremarks').validate({

			highlight: function (input) {
				$(input).parent('.form-group').addClass('has-error');
			},
			unhighlight: function (input) {
				$(input).parent('.form-group').removeClass('has-error');
			},
			errorPlacement: function (error, element) {
				//$(element).parents('.form-group').append(error);
			}
		});
	
	
	$('#Frmremarks').submit(function(e){
		e.preventDefault();
		if($('#Frmremarks').valid()) {
				$.ajax({
				type: "POST",
				url: "<?php echo base_url('admin/markaspiad') ?>",
				data: $("#Frmremarks").serialize(),
				dataType: 'json',
				beforeSend: function (xhr) {
					
					
				  $("#btn_paid").prop("disabled",true);
				  $("#btn_paid").html("<i class='fa fa-spinner fa-spin'></i> Confirm and Pay...");
				},
				success: function (res_data) {
					$("#btn_paid").prop("disabled",false);
					$("#btn_paid").html("Paid");
					if (res_data[0].resSuccess == 1) { 
					  $("#btn_paid").prop("disabled",false);
					  //window.location.href="<?php echo base_url('admin/dashboard') ?>";
						bootbox.alert({ 
							size: "small",
							message: 'Payments Updated Successfully',
							callback:function(){
									window.location.href=window.location.href;
								}
						});	
					
					
					
					}else if (res_data[0].resSuccess == 2){
						  $("#btn_paid").prop("disabled",false);
						bootbox.alert({ 
							size: "small",
							message: 'Unable to update payment',
							callback:function(){
									window.location.href=window.location.href;
								}
						});	
						  
						  
						  
  						if(res_data[0].errtype == 'Validation'){
							//creteToast('Login Error',res_data[0].arrError,'error');
							//$(".msg").html(res_data[0].arrError)
						}					
							return false;
						}					
					}
				});
			}
		return false;
		});
			
		
		
		$('#markaspaid-modal').on('hidden.bs.modal', function () {
			$('#Frmremarks')[0].reset();
			$('#Frmremarks #pay_id').val('0');
			$('#btn_paid').html('Confirm and Pay');
			$('#btn_paid').prop('disabled',false);
		});		

    });
	
	
	function markaspaid(id){
		//alert(id);
		$('#Frmremarks #pay_id').val(id);
		$('#markaspaid-modal').modal('show');
	}
	
	function markaspending(id){
	    if(confirm("Mark this as Pending payment?")){
	        //alert("Yes");
	        $.ajax({
				type: "POST",
				url: "<?php echo base_url('admin/markaspending') ?>",
				data: {'id':id},
				dataType: 'json',
				beforeSend: function (xhr) {
				},
				success: function (res_data) {
					
					if (res_data[0].resSuccess == 1) { 
					  
						bootbox.alert({ 
							size: "small",
							message: 'Payments Updated Successfully',
							callback:function(){
					            window.location.href=window.location.href;
									
								}
						});	
					
					
					
					}else if (res_data[0].resSuccess == 2){
						
						bootbox.alert({ 
							size: "small",
							message: 'Unable to update payment',
							callback:function(){
									window.location.href=window.location.href;
								}
						});	
						  
						  
						  
  						if(res_data[0].errtype == 'Validation'){
							//creteToast('Login Error',res_data[0].arrError,'error');
							//$(".msg").html(res_data[0].arrError)
						}					
							return false;
						}					
					}
				});
	    }else{
	        //alert("No");
	    }
	}
  
  
  
  
  </script>
  
  
  
  