  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        Payments
        <!--<small>it all starts here</small>-->
      </h1>
      <!--<ol class="breadcrumb">
        <li><a href="#"><i class="fa fa-dashboard"></i> Home</a></li>
        <li><a href="#">Examples</a></li>
        <li class="active">Blank page</li>
      </ol>-->
    </section>

    <!-- Main content -->
    <section class="content">

      <!-- Default box -->
      <div class="box">
        
        <div class="box-body">
          <table id="tbl_instructor" class="table table-bordered table-striped">
                <thead>
                <tr>
                  <th>Click to view result</th>
                  <th>Date</th>
                  <th>Amount</th>
                  <th>#Ref No</th>
                  <th>Status</th>
                  <th align="center">Subjects</th>
                </tr>
                </thead>
                <tbody>
                <?php  foreach($payments as $pay){?>
                    <tr>
                      <td><i class="fa fa-file-text-o" aria-hidden="true"></i> <?php echo $pay->title; ?></td>
                   	  <td><?php echo date('d-m-Y',strtotime($pay->payment_date)); ?></td>
                      <td><?php echo '<i class="fa fa-inr"></i> ' . number_format($pay->payment_amount,2); ?></td>
                      <td><?php echo $pay->tnx_reference_no; ?></td>
                      <td><?php echo ($pay->payment_status == 1)?'Paid':'Failed'; ?></td>
                      <td><a href="javascript:void(0);" title="View Subjects" class="fa fa-server" onclick="getSubjects(<?php echo $pay->id ?>)"></a>
                      &nbsp;&nbsp;
                      <?php if($pay->payment_status == 1){ ?>
                      	<a href="<?php echo base_url('rechecking/reprintreceipt/') .  md5($pay->id); ?>" title="Reprint Receipt" class="fa fa-print"></a>
                      <?php } ?>
                      </td>
                    </tr>
               <?php } ?>
                
                </tbody>
                <!--<tfoot>
                <tr>
                  <th>Category Name</th>
                  <th>Task</th>
                  
                </tr>
                </tfoot>-->
              </table>
          
          
          
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
          
        </div>
        <!-- /.box-footer-->
      </div>
      <!-- /.box -->

    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
  
    <div class="modal fade" id="subject-default">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">List of subject applied for Recheckin/Retotaling</h4>
          </div>
          <div class="modal-body" id="subject-box">
            <p><i class='fa fa-spinner fa-spin'></i></p>
          </div>
          
          <div class="modal-footer">
            <button type="button" class="btn btn-default pull-left" data-dismiss="modal">Close</button>
          </div>
        </div>
        <!-- /.modal-content -->
      </div>
      <!-- /.modal-dialog -->
    </div>
    <!-- /.modal -->
  
  
  
  <script>
  
  	$(document).ready(function(e) {

  		$('#subject-default').on('hidden.bs.modal', function () {
			$('#subject-box').html("<i class='fa fa-spinner fa-spin'></i>");
		});		

    });
  
  	function getSubjects(payment_id){
		var params = {
				"payment_id" :payment_id
		};
		$('#subject-default').modal('show', {backdrop: 'static'});
		$.get('<?php echo base_url("getsubject") ?>', params, function (html) {
			$('#subject-box').html(html);
		}); 
		
	}
  
  </script>