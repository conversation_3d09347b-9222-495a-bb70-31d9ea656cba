<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
class Payfail_model extends CI_Model {
	
	private $table = 'tbl_payments tm';
	private $column_order = array('tp.regd_no' ,'ts.name' ,'tp.payment_date' ,'tp.payment_amount'); //set column field database for datatable orderable
	private $column_search = array('tp.regd_no' ,'ts.name' ,'tp.payment_date' ,'tp.payment_amount'); //set column field database for datatable searchable 
	private $order = array('tp.id' => 'DESC'); // default order

	
	
	
	
	

    function __construct()
    {
        // Call the Model constructor
        parent::__construct();
		
    }
	
	
	public function getAllfailedPayment(){
		
        $this->_get_query();
		if(!isset($_POST['length'])){
			$_POST['length']= '10';
		} else if(isset($_POST['length']) && $_POST['length'] < 1) {
			$_POST['length']= '10';
		} else{
			$_POST['length'] = $_POST['length'];
		}
		
		if(!isset($_POST['start'])){
			$_POST['start']= 0;
		}
		else if(isset($_POST['start']) && $_POST['start'] > 1) {
			$_POST['start']= $_POST['start'];
		}
        $this->db->limit($_POST['length'], $_POST['start']);
		
        $query = $this->db->get();
		//echo $this->db->last_query(); exit();
        return $query->result();
    }
	
	
	private function _get_query(){
		
		$result_id =  $this->input->post('result_id');   
		if($this->input->post('tm.stream_full_name')){
			$this->db->like('tm.stream_full_name', $this->input->post('stream_full_name'));
        }
		
		$this->db->select('tp.id,tp.regd_no,tp.result_id,tp.payment_date,tp.payment_amount,
						  tp.payment_status,tp.tnx_reference_no,tp.manually_approved,tp.manual_approved_date,tp.Remarks,ts.name');
		$this->db->from('tbl_payments tp');
		$this->db->join('tbl_students ts','ts.regd_no = tp.regd_no');
		$this->db->where('tp.result_id',$res_id);
		$this->db->where('tp.payment_status','0');

		//$this->db->order_by('tm.stream_code', 'DESC');
				   
        $i = 0;
		
        foreach ($this->column_search as $colmn) // loop column 
        {
			if(isset($_POST['search']['value']) && !empty($_POST['search']['value'])){
				$_POST['search']['value'] = $_POST['search']['value'];
			} else{
				$_POST['search']['value'] = '';
			}				
				
			if($_POST['search']['value']) // if datatable send POST for search
			{
				if($i===0) // first loop
				{
					$this->db->group_start();
					$this->db->like($colmn, $_POST['search']['value']);
				}
				else
				{
					$this->db->or_like($colmn, $_POST['search']['value']);
				}
	
				if(count($this->column_search) - 1 == $i){ //last loop
					$this->db->group_end(); //close bracket
				}
			}
			$i++;
		}
		
		if(isset($_POST['order'])) // here order processing
		{
			$this->db->order_by($this->column_order[$_POST['order']['0']['column']], $_POST['order']['0']['dir']);
		} 
		else if(isset($this->order))
		{
			$order = $this->order;
			$this->db->order_by(key($order), $order[key($order)]);
		}
    }	
	
	
	
	
	
	
	public function  getPaymentInformation(){
		$regd_no = $this->session->userdata('stu_sess_user_id');
		$this->db->select('tp.id,tp.regd_no,tp.result_id,tp.payment_date,tp.payment_amount,tp.payment_status,tp.tnx_reference_no,
						   tp.manually_approved,tp.manual_approved_date,tp.Remarks,tr.title,tr.stream,tr.semester');
		$this->db->from('tbl_payments tp');
		$this->db->join('tbl_result tr','tr.id=tp.result_id');
		$this->db->where('tp.regd_no' , $regd_no);
		$query = $this->db->get();
		return $query->result();
			
	}
	
	
	
	public function getSubjectsBypayId($payment_id){
		$this->db->select('tps.subject_code,tps.subject_name,tps.rechecking_type');
		$this->db->from('tbl_payment_subjects tps');
		$this->db->where(array('payment_id' => $payment_id));
		$query = $this->db->get();
		return $query->result();
	}
	
	
	//-----------------------------------------------------

	
	public function getAdminUserDetails($adminemail){
		$this->db->order_by("ID", "ASC");
		$this->db->where(array('UserEmail' => $adminemail, 'Status' => 1));
        $query = $this->db->get("tbl_admin",1);
        return $query->row();
	}
	
	public function getAdminUserDetailsByEmail($adminemail){
		$this->db->select('UserName, UserEmail');
		$this->db->from('tbl_admin');
		$this->db->where(array('UserEmail' => $adminemail, 'Status' => 1));		
		$query = $this->db->get();		
		return $query->row();
	}
	
	
	public function getRegUsers(){
		$this->db->select('user_id, user_name, user_email, user_status');
		$this->db->from('tbl_users');
		$query = $this->db->get();		
		return $query->result();
		
	}
	
	public function getStatusById($userid){
		
		$this->db->select('user_status');
		$this->db->from('tbl_users');
		$this->db->where(array('user_id' => $userid));		
		$query = $this->db->get();		
		return $query->row();
		
	}
	
	public function updateStatusByID($userid,$status){
		$data = array(
			'user_status' => $status,
		);
		$this->db->where(array('user_id' => $userid));
		$this->db->update('tbl_users', $data);
		//echo $this->db->last_query(); die();
		if($this->db->affected_rows() >=0){
			return true;	
		}else{
			return false;
		}

	}
	
	
	
	public function addcategory($data){
		
		$this->db->insert('tbl_category', $data);
		$insert_id = $this->db->insert_id();
		return ($this->db->affected_rows() != 1) ? false : $insert_id;

	}
	
	public function updateCategory($data,$cat_id){
		$this->db->where(array('cat_id' => $cat_id));
		$this->db->update('tbl_category', $data);
		//echo $this->db->last_query(); die();
		if($this->db->affected_rows() >=0){
			return true;	
		}else{
			return false;
		}
		
	}
	
}