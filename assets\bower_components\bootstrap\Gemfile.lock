GEM
  remote: https://rubygems.org/
  specs:
    addressable (2.6.0)
      public_suffix (>= 2.0.2, < 4.0)
    colorator (1.1.0)
    concurrent-ruby (1.1.4)
    em-websocket (0.5.1)
      eventmachine (>= 0.12.9)
      http_parser.rb (~> 0.6.0)
    eventmachine (1.2.7)
    eventmachine (1.2.7-x64-mingw32)
    ffi (1.10.0)
    ffi (1.10.0-x64-mingw32)
    forwardable-extended (2.6.0)
    http_parser.rb (0.6.0)
    i18n (0.9.5)
      concurrent-ruby (~> 1.0)
    jekyll (3.8.5)
      addressable (~> 2.4)
      colorator (~> 1.0)
      em-websocket (~> 0.5)
      i18n (~> 0.7)
      jekyll-sass-converter (~> 1.0)
      jekyll-watch (~> 2.0)
      kramdown (~> 1.14)
      liquid (~> 4.0)
      mercenary (~> 0.3.3)
      pathutil (~> 0.9)
      rouge (>= 1.7, < 4)
      safe_yaml (~> 1.0)
    jekyll-redirect-from (0.14.0)
      jekyll (~> 3.3)
    jekyll-sass-converter (1.5.2)
      sass (~> 3.4)
    jekyll-sitemap (1.2.0)
      jekyll (~> 3.3)
    jekyll-watch (2.1.2)
      listen (~> 3.0)
    kramdown (1.17.0)
    liquid (4.0.1)
    listen (3.1.5)
      rb-fsevent (~> 0.9, >= 0.9.4)
      rb-inotify (~> 0.9, >= 0.9.7)
      ruby_dep (~> 1.2)
    mercenary (0.3.6)
    pathutil (0.16.2)
      forwardable-extended (~> 2.6)
    public_suffix (3.0.3)
    rb-fsevent (0.10.3)
    rb-inotify (0.10.0)
      ffi (~> 1.0)
    rouge (3.3.0)
    ruby_dep (1.5.0)
    safe_yaml (1.0.4)
    sass (3.7.3)
      sass-listen (~> 4.0.0)
    sass-listen (4.0.0)
      rb-fsevent (~> 0.9, >= 0.9.4)
      rb-inotify (~> 0.9, >= 0.9.7)
    wdm (0.1.1)

PLATFORMS
  ruby
  x64-mingw32

DEPENDENCIES
  jekyll (~> 3.8.5)
  jekyll-redirect-from (~> 0.14.0)
  jekyll-sitemap (~> 1.2.0)
  wdm (~> 0.1.1)

BUNDLED WITH
   1.17.3
