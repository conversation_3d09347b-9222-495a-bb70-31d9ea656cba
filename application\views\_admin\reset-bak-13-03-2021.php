  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
       		Reset Your Password here
        <!--<small>it all starts here</small>-->
      </h1>
      <!--<ol class="breadcrumb">
        <li><a href="#"><i class="fa fa-dashboard"></i> Home</a></li>
        <li><a href="#">Examples</a></li>
        <li class="active">Blank page</li>
      </ol>-->
    </section>

    <!-- Main content -->
    <section class="content">

      <!-- Default box -->
      <div class="box">
      <form id="frmChangpwd" method="post" novalidate>
        <div class="box-header with-border">
         <h3 class="box-title">Change your profile information here</h3>
        </div>
        <div class="box-body">
            <div class="col-sm-4">
                 
                 <div class="form-group">
                      <label for="new_password">New password</label>
                      <input type="password" class="form-control" id="new_password" autocomplete="off" name="new_password" value="" placeholder="New Password" required="required">
                 </div>
                 
                 <div class="form-group">
                      <label for="confirm_password">Confirm password</label>
                      <input type="password" class="form-control" id="confirm_password" autocomplete="off" name="confirm_password" value="" placeholder="Confirm your password" required="required">
                 </div>
             
            </div> 
          
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
            <div class="col-sm-4">
                <button type="submit" class="btn btn-primary btn-sm pull-right" id="btnsave">Reset Password</button>
             </div>   
        </div>
        <!-- /.box-footer-->
      </form>
      
      </div>
      <!-- /.box -->

    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<script src="<?php echo base_url('assets/plugins/jquery-validation/jquery.validate.js') ?>"></script>
  
    <script>
  	$(document).ready(function(e) {
        
			$('#frmChangpwd').validate({
				highlight: function (input) {
					$(input).parent('.form-group').addClass('has-error');
				},
				unhighlight: function (input) {
					$(input).parent('.form-group').removeClass('has-error');
				},
				errorPlacement: function (error, element) {
							//$(element).parents('.form-group').append(error);
				}
			});
	
			$('#frmChangpwd').submit(function(e) {
				
				e.preventDefault();
				if($('#frmChangpwd').valid()) { 
				
						if($('#new_password').val() != $('#confirm_password').val()){
								bootbox.alert({ 
									size: "small",
									message: "Your password and confirmation password do not match."});	
								
								return false;				
						}
				
						$.ajax({
						type: "POST",
						url: "<?php echo base_url('admin/saveprofile') ?>",
						data: $("#frmChangpwd").serialize(),
						dataType: 'json',
						beforeSend: function (xhr) {
						  $("#btnsave").prop("disabled",true);
						  $("#btnsave").html('<i class="fa fa-spinner fa-spin"></i> Saving...');
						  
						},
						success: function (res_data) {
							if (res_data[0].resSuccess == 1) { 
							  //$("#sign_btn").prop("disabled",false);
									$('#modal-category').modal('hide');
									bootbox.alert({ 
										size: "small",
										message: "Profile saved successfully",
										callback:function(){
												window.location.href="<?php echo base_url('admin/logout'); ?>";
											}
												
										});	
							}else if (res_data[0].resSuccess == 2){
								  $("#btnsave").prop("disabled",false);
								   $("#btnsave").html('Save Category');
								   bootbox.alert({ 
										size: "small",
										message: res_data[0].msg,
										callback:function(){
												//window.location.href=window.location.href;
											}
									});	
						
									return false;
								}					
							}
						});
					}
				return false;
				});
		
		
		});
		
		
 </script>