<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=yes" name="viewport">
  <!-- Bootstrap 3.3.7 -->
  <!-- Font Awesome -->
  <link rel="stylesheet" href="<?php echo base_url('assets/bower_components/font-awesome/css/font-awesome.min.css') ?>">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,600,700,300italic,400italic,600italic">
<title><?php echo APP_TITLE; ?></title>
<style>
body {
    font-family: 'Source Sans Pro','Helvetica Neue',Helvetica,Arial,sans-serif;
    font-weight: 400;
    overflow-x: hidden;
    overflow-y: auto;
}
.main-table{
  border: 1px solid black;
  /*border-collapse: collapse;*/
  max-width:680px;
  min-width:680px;
  padding:5px;
  font-family:sans-serif;
  font-size:13px;
}
.btn {
    display: inline-block;
    margin-bottom: 0;
    font-weight: 400;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    cursor: pointer;
    background-image: none;
    border: 1px solid transparent;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.42857143;
    border-radius: 4px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
.btn-primary {
    background-color: #3c8dbc;
    border-color: #367fa9;
	color:#fff;
}
.btn-default {
    background-color: #f4f4f4;
    color: #444;
    border-color: #ddd;
}
a { text-decoration:none;}
@media print {
	 .print-column{ display:none;}
	 .main-table{
		  border: 1px solid black;
		  border-collapse: collapse;
		  max-width:680px;
		  min-width:680px;
		  padding:5px;
		  font-family:sans-serif;
		  font-size:13px;
	}
}

</style>
</head>

<body>
	<table class="main-table" align="center">
    <tr>
    	<td align="center" colspan="5">
        	<img src="https://www.cet.edu.in/images/home_04.jpg" alt="Logo" style="width:45px;"/>
        </td>
    </tr>
    <tr>
    	<td colspan="5"></td>
    </tr>
    <tr align="center">
    	<td colspan="5">COLLEGE OF ENGINEERING AND TECHNOLOGY<br /><span style="color:#C8000C;">(AUTONOMOUS)</span></td>
    </tr>	
    <?php if(!empty($subjectspd)){ $i=1;  
		foreach($subjectspd as $subpd){ ?>
        	<?php if($i == 1){ ?>
            	<tr>
                    <td colspan="5" style="background-color:#002060;color:#fff; padding:5px 0px 5px 0px;  text-align:left;">Payment Successful</td>
                </tr>
                <tr>
                    <td colspan="2" align="left">Registration No:</td>
                    <td colspan="3" style="color:#1C9AC2;font-weight:bold;"><?php echo $subpd->regd_no; ?></td>
                </tr>
                <tr>
                    <td colspan="2" align="left">Student Name:</td>
                    <td colspan="3" style="color:#003399;font-weight:bold;"><?php echo $subpd->name; ?></td>
                </tr>
                <tr>
                    <td colspan="2" align="left">Branch:</td>
                    <td colspan="3" style="color:#2A9630;font-weight:bold;"><?php echo $subpd->stream; ?></td>
                </tr>
                <tr>
                    <td colspan="2" align="left">Semester:</td>
                    <td colspan="3" style="color:#2A9630;font-weight:bold;"><?php echo $subpd->semester; ?></td>
                </tr>
                <tr>
                    <td colspan="5" align="left">&nbsp;</td>
                </tr>             
                <tr>
                    <th align="left" width="10%">SL. No.</th>
                    <th align="left" width="17%">Subject Code</th>
                    <th align="left" width="53%">Subject</th>
                    <th align="center" width="10%">Rechecking/Retotaling</th>
                    <th align="center" width="10%">Amount</th>                    
                </tr>
             <?php } ?>
             <tr>
                <td align="center"><?php echo $i ?></td>
                <td><?php echo $subpd->subject_code; ?></td>
                <td><?php echo $subpd->subject_name; ?></td>                
                <td align="center"><?php if($subpd->rechecking_type == 1){ echo 'Rechecking'; }else{ echo 'Rechecking with photocopy'; } ?></td>
                <td align="right"><?php if($subpd->rechecking_type == 1){ echo '500.00'; }else{ echo '1000.00'; } ?>&nbsp;</td>
            </tr>            
            <?php if($i == count($subjectspd)){ ?>
            	<tr>
                    <td colspan="5" align="left">&nbsp;</td>
                </tr>
                <tr>
                	<td align="left" colspan="2">No. of subject selected:</td>
                    <td colspan="3" align="left"><?php echo count($subjectspd); ?></td>
                </tr>
                                
                <tr>
                	<td align="left">Txn No.:</td>
                    <td align="left"><b><?php echo $subpd->tnx_reference_no; ?></b></td>
                    <td align="right" colspan="3">&nbsp;</td>                    
                </tr>                                
                <tr>
                	<td align="left">Date:</td>
                    <td align="left"><b><?php echo $subpd->payment_date; ?></b></td>
                    <td align="right">&nbsp;</td>
                    <td align="right">Total Amount:</td>
                    <td align="right"><b><i class="fa fa-inr"></i>&nbsp;<?php echo number_format($subpd->payment_amount, 2, '.', ''); ?></b>&nbsp;</td>
                </tr>
                <tr>
                    <td colspan="5" align="left">&nbsp;</td>
                </tr>
            <?php } ?>
    <?php $i++; } ?>      
    <tr align="center" class="print-column">
    	<td colspan="5"><a href="javascript:void(0);" class="btn btn-primary" onclick="window.print()">Print</a>&nbsp;<a href="<?php if($receipttype == 2){ echo base_url('payments'); }else{ echo base_url('student/logout'); } ?>" class="btn btn-default">Exit</a></td>
    
    </tr>
    <?php }else{ ?>
    	<tr>
            <td colspan="5" style="background-color:#002060;color:#fff; padding:5px 0px 5px 0px;  text-align:left;">Payment Failed</td>
        </tr>
        <tr align="center" class="print-column">
    	<td colspan="5"><a href="<?php if($receipttype == 2){ echo base_url('payments'); }else{ echo base_url('student/logout'); } ?>" class="btn btn-default">Exit</a></td>
    
    </tr>
    <?php } ?>
</table>

</body>
</html>