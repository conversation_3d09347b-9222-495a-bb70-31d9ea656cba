{"name": "datatables.net", "description": "DataTables for jQuery ", "main": ["js/jquery.dataTables.js"], "keywords": ["filter", "sort", "DataTables", "j<PERSON><PERSON><PERSON>", "table", "DataTables"], "dependencies": {"jquery": ">=1.7"}, "moduleType": ["globals", "amd", "node"], "ignore": ["composer.json", "datatables.json", "package.json"], "authors": [{"name": "SpryMedia Ltd", "homepage": "https://datatables.net"}], "homepage": "https://datatables.net", "license": "MIT", "version": "1.10.19", "_release": "1.10.19", "_resolution": {"type": "version", "tag": "1.10.19", "commit": "43336fb944724a42f59748c72ca3728c9ec13cf6"}, "_source": "https://github.com/DataTables/Dist-DataTables.git", "_target": "^1.10.15", "_originalSource": "datatables.net"}