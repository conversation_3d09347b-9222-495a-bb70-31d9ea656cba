/**
 * French (Switzerland) translation for bootstrap-datepicker
 * <PERSON> <<EMAIL>>
 * Based on 
 * French translation for bootstrap-datepicker
 * <PERSON> <<EMAIL>>
 */
;(function($){
	$.fn.datepicker.dates['fr'] = {
		days: ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>red<PERSON>", "<PERSON><PERSON>"],
		daysShort: ["Di<PERSON>", "Lun", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "V<PERSON>", "<PERSON>"],
		daysMin: ["D", "<PERSON>", "Ma", "Me", "J", "V", "S"],
		months: ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Avril", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Septembre", "Octobre", "Novembre", "Décembre"],
		monthsShort: ["<PERSON>", "<PERSON>é<PERSON>", "<PERSON>", "<PERSON>v<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Oct", "Nov", "Déc"],
		today: "Aujourd'hui",
		monthsTitle: "Mo<PERSON>",
		clear: "Effacer",
		weekStart: 1,
		format: "dd.mm.yyyy"
	};
}(jQuery));
