  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        Result Set
        <!--<small>it all starts here</small>-->
      </h1>
      <ol class="breadcrumb">
        <li><a href="<?php echo base_url('admin/dashboard'); ?>"><i class="fa fa-dashboard"></i> Home</a></li>
        <li>Result Set</li>
        <li class="active">Student list by Regdno</li>
      </ol>
    </section>

    <!-- Main content -->
    <section class="content">

      <!-- Default box -->
      <div class="box">
      
        <div class="col-sm-12" style="background-color:#dedede; margin-bottom:10px;">
                            
           <div class="row"> 
            <table class="table" style="margin-bottom:0px;">
                <tr>
                    <td width="5%"><strong>Title</strong></td>
                    <td width="1%">:</td>	
                    <td width="69%"><?php echo $resultset->title; ?></td>
               		<td><a href="<?php echo base_url('admin/importresult') ?>/<?php echo $resultset->id ?>" class="btn btn-sm btn-primary pull-right">Re-Import Excel</a></td>
                </tr>
                
                <tr>
                    <td><strong>Stream</strong></td>
                    <td>:</td>	
                    <td><?php echo $resultset->stream; ?></td>
                    <td></td>
                </tr>
                
                <tr>
                    <td><strong>Semester</strong></td>
                    <td>:</td>	
                    <td><?php echo $resultset->semester; ?> </td>
                    <td></td>
                </tr>
            
            </table>
          </div>  
            
        </div>      
      
        <div class="box-body">
        
           
                <table id="tbl_stu_grp" class="table table-bordered table-striped">
                    <thead>
                    <tr>
                      <th>Regdno</th>
                      <th>Name</th>
                      <th>Email</th>
                      <th data-orderable="false" style="text-align:center;">Task</th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php  foreach($students as $stu){?>
                        <tr>
                          <td><?php echo $stu->regd_no; ?></td>
                          <td><?php echo $stu->name; ?></td>
                          <td><?php echo $stu->email; ?></td>
                             <td align="center"> 
                                
                                <a href="<?php echo base_url('admin/viewresult') ?>/<?php echo $stu->regd_no ?>/<?php echo md5($resultset->id) ?>" title="View <?php echo $resultset->title; ?>" class="btn btn-sm btn-success">
                                    <i class="fa fa-eye"></i> 
                                </a>
                                
                                <a href="javascript:void(0);" onclick="deleteResultSet(<?php echo $resultset->id ?>,<?php echo $stu->regd_no; ?>);" title="Delete  <?php echo $resultset->title; ?>" class="btn btn-sm btn-danger btn_<?php echo $stu->regd_no; ?>">
                                    <i class="fa fa-trash"></i> 
                                </a>
                                
                            </td>
                        </tr>
                   <?php } ?>
                    
                    </tbody>
    
                  </table>
           
          
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
             
        </div>
        <!-- /.box-footer-->
    
      
      </div>
      <!-- /.box -->

    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
  <script>

	$(document).ready(function(e) {
        
		$('#tbl_stu_grp').DataTable({
					
					'paging'      : true,
					'searching'   : true,
					'ordering'    : true,
					'info'        : true,
					'autoWidth'   : false,
					"bLengthChange": false,
					"order": []
				
		});		
		
    });

	
	function deleteResultSet(result_id,regd_no){
		bootbox.confirm({ 
			size: "small",
			message: "Are you sure? You want to delete this.",
			callback: function(result){ /* result is a boolean; true = OK, false = Cancel*/
					if(result){
							$.ajax({
								type: "POST",
								url: "<?php echo base_url('admin/deletesturesultset') ?>",
								data: {'result_id':result_id,'regd_no':regd_no},
								dataType: 'json',
								beforeSend: function (xhr) {
								  $(".btn_" + regd_no).prop("disabled",true);
								  $(".btn_" + regd_no).html('<i class="fa fa-spinner fa-spin"></i>');
								  
								},
								success: function (res_data) {
									  $(".btn_" + regd_no).prop("disabled",false);
									  $(".btn_" + regd_no).html('<i class="fa fa-trash"></i>');
									
									if (res_data[0].resSuccess == 1) { 

										
										bootbox.alert({ 
											size: "small",
											message: "Result deleted sucessfully.",
											callback:function(){
													window.location.href=window.location.href;
												}
													
											});	
									  
									}else if (res_data[0].resSuccess == 2){
										
											 bootbox.alert({ 
												size: "small",
												message: res_data[0].msg});	
											}					
									}
								});
						
					}
			 }
		})
		
	}	
  
  </script>
  
  
  
  