<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Rechecking extends CI_Controller {
	
	function __construct() {
        parent::__construct();		
		$this->load->model('rechecking_model', 'recheckingModel');
	}
	
		
	public function rechecking(){
		$this->load->library('Student_acess');
		$data["menu"] = "rechecking";
		$data['results'] = $this->recheckingModel->getResult();
		$this->load->view('_student_shared/header',$data);	
		$this->load->view('_rechecking/resultset',$data);
		$this->load->view('_student_shared/footer',$data);
		
	}
	
	
	
	public function subjectlist($result_id){
		$this->load->library('Student_acess');
		$data["menu"] = "rechecking";
		$data["result"] =  $this->recheckingModel->getResultById($result_id);
		$data['subjects'] = $this->recheckingModel->getSubjectByResultId($result_id);
		$this->load->view('_student_shared/header',$data);	
		$this->load->view('_rechecking/subjectlist',$data);
		$this->load->view('_student_shared/footer',$data);
	}
	
	
	public function saverechecking(){
		
		$this->load->library('Student_acess');
		$data["menu"] = "rechecking";

		$recheck = $this->input->post('recheck',TRUE);
		$result_id = $this->input->post('result_id',TRUE);
		
		//print_r($this->session->userdata()); exit('==test');
		
		if(!empty($recheck)){		
		
			$payment_details_arr = array();
			$regd_no = $this->session->userdata('stu_sess_user_id');
			$student_name = $this->session->userdata('stu_sess_user_name');
	
			$this->db->trans_strict(TRUE);	
			$this->db->trans_begin();
			
			$txn_no = "RS" . $regd_no . date("YmdHis");
			$payment_arr = array('regd_no' => $regd_no,
								  'result_id' => $result_id,
								  'payment_date' => date('Y-m-d'),
				                  'txn_no' => $txn_no,
				                  'created_at' => date('Y-m-d H:i:s'));
			
			$payment_id = $this->recheckingModel->insertPayment($payment_arr);
			$total_amt = 0.00;
			
			foreach($recheck as $key => $val){
				
				$key_vl = explode('-',$val);
				$subject = $this->recheckingModel->getSubjectBySubCode($key_vl[0], $regd_no);
				$rechicking_type = $this->input->post('rechecking_'.$key_vl[1],TRUE);
				
				if($rechicking_type == 1){
					$total_amt+=500.00;
				}else if($rechicking_type == 2){
					$total_amt+=1000.00;
				}
				
				
				$payment_details_arr[] = array('payment_id' => $payment_id,
								   'subject_code' =>$key_vl[0] ,
								   'subject_name' =>$subject->subject_name,
								   'rechecking_type'=>$rechicking_type);			
			
			}
			
			$this->recheckingModel->insertPaymentSubjects($payment_details_arr);

			
			if($this->db->trans_status() === TRUE){
				$this->db->trans_commit();
				
				if($payment_id > 0){
				
					$checksum_key = "blnbqVUlfb9C";
					$tran_no = $regd_no . date("YmdHis");
					//$total_amt = '1.00';
					$str = 'COLLEGEET|' . $tran_no . '|NA|' . number_format($total_amt, 2, '.', '') . '|NA|NA|NA|INR|NA|R|collegeet|NA|NA|F|' . $student_name . '|RESDB|NA|NA|'.$regd_no.'|' . $payment_id . '|NA|https://results.cet.edu.in/rechecking/response';					
	
					$checksum = hash_hmac('sha256',$str,$checksum_key, false); 				
					$checksum = strtoupper($checksum);
					$msg = $str . "|" . $checksum;	
					
					$final_msg = $msg;
					
					$result[] = array('resSuccess' => 1, 'final_msg' => $final_msg);
					echo json_encode($result);
					exit();
								
				}else{
					
					$result[] = array('resSuccess' => 2, 'msg' => 'Unable to process request');
					echo json_encode($result);			
					exit();				
				}
				
				 
				/*$result[] = array('resSuccess' => 1, 'msg' => 'Data saved successfully');
				echo json_encode($result);
				exit();*/				
				
			}else{
				 $this->db->trans_rollback(); 
				$result[] = array('resSuccess' => 2, 'msg' => 'Unable to save data');
				echo json_encode($result);			
				exit();
			}
							
		}else{
			
			$result[] = array('resSuccess' => 2, 'msg' => 'Please specify subject(s)');
			echo json_encode($result);			
			exit();			
		}		
	}
	
	public function response(){
		
		$this->db->trans_strict(TRUE);	
		$this->db->trans_begin();
		
		/*Array ( [0] => COLLEGEET [1] => 1111111111 [2] => VUR29977250029 [3] => 113717169159 [4] => 1.00 [5] => UR2 [6] => 608023 [7] => 03 [8] => INR [9] => RDDIRECT [10] => NA-994130 [11] => NA [12] => 00000000.00 [13] => 17-05-2021 17:08:08 [14] => 0300 [15] => NA [16] => DEMO STUDENT [17] => NA [18] => NA [19] => NA [20] => NA [21] => 5 [22] => NA [23] => NA [24] => PGS10001-Success [25] => 55F6D9147F5A94F6CC45B1193D9BB578CA811A663B1AC7E1C502736F811F1D74 )*/
		$stringData = "";			
		if(isset($_POST['msg'])){
			$stringData .= $_POST['msg'] . "\n";
		}		
				
		if($stringData != ""){
			$myFile = "/home/<USER>/rechktxnfiles/trans_" . date('m-d-Y H:i:s') . ".txt";				
			$fh = fopen($myFile, 'a');
			fwrite($fh, $stringData);		
			fclose($fh);		
		}
						
		$final_msg_exp = array();
		
		if(isset($_POST['msg'])){		
			$final_msg = $_POST['msg'];
			$final_msg_exp = explode('|', $_POST['msg']);
									
			if(!empty($final_msg_exp)){
				
				$regd_no = $final_msg_exp[20];
				$txn_referenceno = $final_msg_exp[2];
				//$bank_referenceno = $final_msg_exp[3];
				$txn_amount = $final_msg_exp[4];
				//$bdtxn_date = $final_msg_exp[13];			
				$txn_date = date('Y-m-d');							
				$txn_status = $final_msg_exp[14];			
				//$student_name = $final_msg_exp[16];
				$posteddb = $final_msg_exp[17]; 				
				//$this->new_txn_id = $final_msg_exp[20]; 
				$payment_id =	$final_msg_exp[21];				
				$txn_msg = $final_msg_exp[24];
				
				
				$checksum_key = "blnbqVUlfb9C";	
				$code = substr(strrchr($final_msg, "|"), 1); //Last check sum value			
				$str = str_replace("|" . $code, "", $final_msg); //string replace : with empty space						
				$checksum = hash_hmac('sha256', $str, $checksum_key, false); // calculated  check sum					
				$checksum = strtoupper($checksum);
								
				//echo $txn_status . " == " . $payment_id . " == " . $checksum . " == " . $code;
								
				if($txn_status == '0300' && $checksum == $code){
				
					$dat_pay_update = array('payment_date' => $txn_date,
										'payment_amount' => $txn_amount,
										'payment_status' => '1',
										'tnx_reference_no' => $txn_referenceno,
										'error_code' => $txn_status,
						                'error_message' => $txn_msg,
						                'updated_at' => date('Y-m-d H:i:s'));
					
					$this->recheckingModel->updatePayment($dat_pay_update, $payment_id);
					
					if($this->db->trans_status() === TRUE){
						$this->db->trans_commit();
						$ptransaction_msg = "Payment Successful";
						$payment_idx = md5($payment_id);
						$data['subjectspd'] = $this->recheckingModel->getSubjectsByPaymentDetails($payment_idx); 					
					}else{
						$this->db->trans_rollback(); 
						$ptransaction_msg = "Payment Failed";
						$data['subjectspd'] = "";
					}										
				}else{
					$ptransaction_msg = "Payment Failed";	
					$data['subjectspd'] = "";
				}
			}
			
		}else{
			$ptransaction_msg = "Payment Failed";
			$data['subjectspd'] = "";					
		}
		
		$data['menu'] = "Receipt";
		$data['ptxnmsg'] = $ptransaction_msg;
		$data['receipttype'] = 1;
								
		$this->load->view('_rechecking/response', $data);
		
	}
	
	public function reprintreceipt($pid){
		
		$data['menu'] = "Receipt";
		$data['subjectspd'] = $this->recheckingModel->getSubjectsByPaymentDetails($pid);
		$data['ptxnmsg'] = "Payment Successful";
		$data['receipttype'] = 2;								
		
		$this->load->view('_rechecking/response', $data);
	}
		
	public function payments(){

		$this->load->library('Student_acess');
		$data["menu"] = "payments";
		
		$data['payments'] = $this->recheckingModel->getPaymentInformation();
		//print_r($data['payments']); die('ssss');
		$this->load->view('_student_shared/header',$data);	
		$this->load->view('_rechecking/payment',$data);
		$this->load->view('_student_shared/footer',$data);
		
	}
	
	public function getsubject(){
		$this->load->library('Student_acess');
		$data["menu"] = "payments";
		$payment_id = $this->input->get('payment_id'); 
		$data['subjects'] = $this->recheckingModel->getSubjectsBypayId($payment_id);
		//print_r($data['subjects']); die();
		$sub = $this->load->view('_rechecking/paidsubject',$data,TRUE);
	
		echo $sub; 
	}

	function generateRandomString($length) {
		return substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length/strlen($x)) )),1,$length);
	}	

}
