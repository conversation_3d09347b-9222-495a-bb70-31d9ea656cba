  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        Student List
        <!--<small>it all starts here</small>-->
      </h1>
      <ol class="breadcrumb">
        <li><a href="<?php echo base_url('admin/dashboard'); ?>"><i class="fa fa-dashboard"></i> Dashboard</a></li>
        <li class="active">Student List</li>
      </ol>
    </section>

    <!-- Main content -->
    <section class="content">

      <!-- Default box -->
      <div class="box">
     
        <div class="box-body">
			<table id="tbl_stu_set" class="table table-bordered table-striped">
                <thead>
                <tr>
                  <th>Regno</th>
                  <th>Name</th>
                  <th>Email</th>
                  <th>Password</th>
                  
                </tr>
                </thead>
                <tbody>
                <?php  foreach($students as $stu){?>
                    <tr>
                      <td><?php echo $stu->regd_no; ?></td>
                      <td><?php echo $stu->name; ?></td>
                      <td><?php echo $stu->email; ?></td>
                       <td><?php echo $this->encryption->decrypt($stu->password); ?></td>
                    </tr>
               <?php } ?>
                
                </tbody>
                <!--<tfoot>
                <tr>
                  <th>Category Name</th>
                  <th>Task</th>
                  
                </tr>
                </tfoot>-->
              </table>

          
        </div>
        <!-- /.box-body -->
        <div class="box-footer">
             
        </div>
        <!-- /.box-footer-->
    
      
      </div>
      <!-- /.box -->

    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
  <script>
  	$(document).ready(function(e) {
       $('#tbl_stu_set').DataTable({
			
			'paging'      : true,
			'searching'   : true,
			'ordering'    : true,
			'info'        : true,
			'autoWidth'   : false,
			"bLengthChange": false,
			"order": []
		
		});

    });
  
  </script>