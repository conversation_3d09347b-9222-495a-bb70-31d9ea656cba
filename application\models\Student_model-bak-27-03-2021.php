<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
class Student_model extends CI_Model {

    function __construct()
    {
        // Call the Model constructor
        parent::__construct();
		
    }
	
	public function validate_login($regd_no,$password){
		
		$this->db->select('regd_no,name,email,password');
		$this->db->from('tbl_students');
		
		$this->db->where(array('regd_no' => $regd_no));
        $query = $this->db->get();
		
		if($query->num_rows() > 0 ){
			$row = $query->row();
			if($this->encryption->decrypt($row->password) == $password){
				$this->session->set_userdata('stu_sess_user_id',$row->regd_no);
				$this->session->set_userdata('stu_sess_user_name',ucfirst($row->name));
				$this->session->set_userdata('stu_sess_user_email',$row->email);
				$this->session->set_userdata('stu_sess_user_domain','');
				$this->session->set_userdata('stu_sess_login',1);	
				return true;
			}else{
				return false;	
			}
		}else{
			return false;	
		}
	}
	
	public function validatemail($email){
		
		$this->db->select('id');
		$this->db->from('tbl_admin');
		$this->db->where('email', $email);
		$query = $this->db->get();
		if(!empty($query->row())){
			return true;
		}else{
			return false;
		}

	}
	
	
	public function getResult(){
		$regd_no = $this->session->userdata('stu_sess_user_id');
		$this->db->select('tr.id,tr.title,tr.stream,tr.semester,tr.publish_date,tr.rechecking_allowed,tr.start_date,tr.end_date,tr.result_type');
		$this->db->from('tbl_result tr');
		$this->db->where("EXISTS(SELECT regd_no FROM tbl_result_details trd where trd.result_id = tr.id  and trd.regd_no = " . $regd_no.")");
		//$this->db->join('tbl_result_details trd', 'tr.id = trd.result_id');
		$query = $this->db->get();
		//echo $this->db->last_query();
		return $query->result();
	}
	
	public function getStudentResult($result_id){
		$regd_no = $this->session->userdata('stu_sess_user_id');
		$this->db->select('tr.id,tr.title,tr.stream,tr.semester,tr.publish_date,tr.rechecking_allowed,tr.start_date,tr.end_date,tr.result_type,
						   trd.branch,trd.subject_code,trd.subject_name,trd.credits,trd.grade,trd.sgpa,trd.subject_type');
		$this->db->from('tbl_result tr');
		$this->db->join('tbl_result_details trd', 'tr.id = trd.result_id');
		$this->db->where(array('trd.regd_no' => $regd_no, 'md5(trd.result_id)' => $result_id));
		$query = $this->db->get();
		//echo $this->db->last_query(); die('xxx');
		return $query->result();
		
	}
	
	
	public function getAdmindetails($email=''){
		$this->db->select('password');
		$this->db->from('tbl_admin');
		$this->db->where(array('id' => $this->session->userdata('sess_user_id')));
        $query = $this->db->get();
		return $query->row();		
	}
	
	
	
	public function updateProfile($data){ 
		$id = $this->session->userdata('sess_user_id');
		$this->db->where(array('id' => $id));
		$this->db->update('tbl_admin', $data);
		//echo $this->db->last_query(); die();
		if($this->db->affected_rows() >=0){
			return true;	
		}else{
			return false;
		}

		
	}
	
	
	
	
	
	public function deleteFiles($path){
		$file = $path; 
		if(is_file($file)){
			unlink($file); 
		}
		return 1;   
	}
	
	public function getAdminUserDetails($adminemail){
		$this->db->order_by("ID", "ASC");
		$this->db->where(array('UserEmail' => $adminemail, 'Status' => 1));
        $query = $this->db->get("tbl_admin",1);
        return $query->row();
	}
	
	public function getAdminUserDetailsByEmail($adminemail){
		$this->db->select('UserName, UserEmail');
		$this->db->from('tbl_admin');
		$this->db->where(array('UserEmail' => $adminemail, 'Status' => 1));		
		$query = $this->db->get();		
		return $query->row();
	}
	
	
	public function getRegUsers(){
		$this->db->select('user_id, user_name, user_email, user_status');
		$this->db->from('tbl_users');
		$query = $this->db->get();		
		return $query->result();
		
	}
	
	public function getStatusById($userid){
		
		$this->db->select('user_status');
		$this->db->from('tbl_users');
		$this->db->where(array('user_id' => $userid));		
		$query = $this->db->get();		
		return $query->row();
		
	}
	
	public function updateStatusByID($userid,$status){
		$data = array(
			'user_status' => $status,
		);
		$this->db->where(array('user_id' => $userid));
		$this->db->update('tbl_users', $data);
		//echo $this->db->last_query(); die();
		if($this->db->affected_rows() >=0){
			return true;	
		}else{
			return false;
		}

	}
	
	
	
	public function addcategory($data){
		
		$this->db->insert('tbl_category', $data);
		$insert_id = $this->db->insert_id();
		return ($this->db->affected_rows() != 1) ? false : $insert_id;

	}
	
	public function updateCategory($data,$cat_id){
		$this->db->where(array('cat_id' => $cat_id));
		$this->db->update('tbl_category', $data);
		//echo $this->db->last_query(); die();
		if($this->db->affected_rows() >=0){
			return true;	
		}else{
			return false;
		}
		
	}
	
}