<?php
defined('BASEPATH') OR exit('No direct script access allowed');
require 'vendor/autoload.php';
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class Admin extends CI_Controller {
	
	function __construct() {
        parent::__construct();
		
		$this->load->helper('text');
		$this->load->library(array('encryption', 'form_validation'));
		$this->load->model('admin_model', 'adminModel');
		
		//echo $this->encryption->encrypt('admin'); die();

	}
		
	public function index(){ 
		$this->load->view('_admin/login');
	}
	
	public function login(){
		$result = array();
		$this->form_validation->set_rules('userid', "userid", 'trim|required|callback__validate_login');
		if ($this->form_validation->run() == FALSE){
			$result[] = array('resSuccess' => 2, 'msg' => 'Error', 'errtype' => 'Validation', 'arrError' => validation_errors());
			echo json_encode($result);			
			exit();
		}else{
			$result[] = array('resSuccess' => 1, 'msg' => 'Success');
			echo json_encode($result);
			exit();				
		}

		
	}
	
	public function _validate_login(){
		
		$userid = trim($this->input->post("userid"));
		$pwd = trim($this->input->post("password"));

		if($userid!="" && $pwd!=""){
			if($this->adminModel->validate_login($userid,$pwd)){
				return true;	
			}else{
				$this->form_validation->set_message('_validate_login', 'Unauthenticated User! Access Denied.');
          		return false;
			}
		}
	}
	
	public function forgotpwd(){
		$data = array();
		$data['menu'] = '';
		//$this->load->view('_admin_shared/header',$data);	
		$this->load->view('_admin/forgot',$data);
		//$this->load->view('_admin_shared/footer',$data);
		
	}
	
	
	public function sendpwd(){
		$data = array();
		$data['menu'] = '';
		$email = $this->input->post('email',TRUE);
		
		
		$admin_dt = $this->adminModel->getUserByEmail($email);
		if(!empty($admin_dt)){
			
			$id = $admin_dt->id;
			$str_result = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'; 
			$length_of_string = 5;
			$token = substr(str_shuffle($str_result),  0, $length_of_string);
			$token = md5($token); 
			$data['id'] = md5($admin_dt->id); 
			
			$msg = $this->load->view('_admin/email',$data,TRUE);
			
			//echo $msg; die('ssss');
			
			$from_email =  $admin_dt->email;
			//$to_email = $admin_dt->email;
			$to_email = "<EMAIL>";
			
			$config = array(
				//'mailpath' => '/usr/sbin/sendmail',
				'protocol' => 'smtp',
				'smtp_host' => $this->config->item('smtp_host'),
				'smtp_port' => $this->config->item('smtp_port'),
				'smtp_user' => $this->config->item('mail_id'),
				'smtp_pass' => $this->config->item('mail_pwd'),
				'mailtype'  => 'html', 
				'charset'   => 'iso-8859-1'
			);
			
			//print_r($config); die();
			
			$this->load->library('email',$config);
			$this->email->set_newline("\r\n");
			
			$this->email->from($from_email, 'CET - RESULT'); 
			$this->email->to($to_email);
			$this->email->subject('Password Reset'); 
			$this->email->message($msg); 
	
			//Send mail 
			if (!$this->email->send()) {
				//echo $this->email->print_debugger()
				//$result[] = array('resSuccess' => 2, 'msg' => 'Unable To send msg.');
				$result[] = array('resSuccess' => 2, 'msg' => $this->email->print_debugger());
				echo json_encode($result);
				exit();	
			}else{
				$result[] = array('resSuccess' => 1, 'msg' => 'Success');
				echo json_encode($result);
				exit();	
			}
			
		}else{
			$result[] = array('resSuccess' => 2, 'msg' => $email . ' is not registred.');
			echo json_encode($result);			
			exit();
			
		}
		
		
		
		
		
		
		if(!$this->adminModel->validatemail($email)){
			$result[] = array('resSuccess' => 2, 'msg' => $email . ' is not registred.');
			echo json_encode($result);			
			exit();
		}else{
			
			
			//$token = md5()
			
			
			
			
			
			$result[] = array('resSuccess' => 2, 'msg' => $email . ' is  registred.');
			echo json_encode($result);			
			exit();
	
		}
		//echo $email; die();
		
		
	}
	
	
	
	public function sendmail(){
		
		
		
		$this->load->view('_admin/email');
		
	}
	
	
	
	
	
	
	public function resetpwd($id,$token){
		
		//echo $id; die('www');
		$admin_dt = $this->adminModel->getadmindetailsByMd5ID($id,$token);
		if(!empty($admin_dt)){
			
			$data['menu'] = '';
			
			
			
			
			$this->load->view('_admin_shared/header',$data);
			$this->load->view('_admin/reset',$data);
			$this->load->view('_admin_shared/footer',$data);
			
		}else{
			$this->load->view('_admin/error');	
			
		}
	
	
	}
	
	public function logout(){
		
		$this->session->sess_destroy();
		redirect('admin/');

	}
	
	public function dashboard(){
		
		$this->load->library('Admin_acess');
		$data["menu"] = "dashboard";
		$this->load->view('_admin_shared/header',$data);	
		$this->load->view('_admin/dashboard',$data);
		$this->load->view('_admin_shared/footer',$data);
	}
	
	
	public function changepwd(){
		$this->load->library('Admin_acess');
		$data["menu"] = "";
		$this->load->view('_admin_shared/header',$data);	
		$this->load->view('_admin/changepwd',$data);
		$this->load->view('_admin_shared/footer',$data);
		
	}
	
	
	
	public function saveprofile(){
		$this->load->library('Admin_acess');
		$data["menu"] = "";

		$name = $this->input->post('name',TRUE);
		$email = $this->input->post('email',TRUE);
		$old_password = $this->input->post('old_password',TRUE);
		$new_password = $this->input->post('new_password',TRUE);
		
		
		$this->db->trans_strict(TRUE);	
		$this->db->trans_begin();

			$profile_info = $this->adminModel->getAdmindetails();
			if(!empty($profile_info)){
				
				if($this->encryption->decrypt($profile_info->password) != $old_password){
					$result[] = array('resSuccess' => 2, 'msg' => 'Incorrect old password provided');
					echo json_encode($result);			
					exit();
				}else{
					
					if(!empty($new_password)){
						
						$data_pwd = array('password' => $this->encryption->encrypt($new_password));
						$this->adminModel->updateProfile($data_pwd);
					}
				}
				
			}else{
					$result[] = array('resSuccess' => 2, 'msg' => 'Profile not found.');
					echo json_encode($result);			
					exit();
			}
			
		
		if($this->db->trans_status() === TRUE){
			$this->db->trans_commit(); 
			$result[] = array('resSuccess' => 1, 'msg' => 'Profile saved successfully');
			echo json_encode($result);
			exit();				
			
		}else{
			 $this->db->trans_rollback(); 
			$result[] = array('resSuccess' => 2, 'msg' => 'Unable to save profile');
			echo json_encode($result);			
			exit();
		}	
		
			
		
	}
	
	
	
	public function importresult($res_id = ''){
		$this->load->library('Admin_acess');
		$data["menu"] = "importresult";
		$data['streams'] = $this->config->item('streams');
		$data['res_id'] = $res_id;
		
		if(!empty($res_id)){
			$data['results'] = $this->adminModel->getResutByresultId($res_id);
		}
		
		$this->load->view('_admin_shared/header',$data);	
		$this->load->view('_admin/import_result',$data);
		$this->load->view('_admin_shared/footer',$data);
		
		
	}
	
	
	
	
	public function studentinfo(){
		$this->load->library('Admin_acess');
		$data["menu"] = "studentinfo";
		
		$file_name = 'assets/student_result_excel/result_data_v2.xlsx';
		$file_type = \PhpOffice\PhpSpreadsheet\IOFactory::identify($file_name);
 		$reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($file_type);
		
 		$spreadsheet = $reader->load($file_name);
		$data['stu_data'] = $spreadsheet->getActiveSheet()->toArray();
		$data['success_record'] = array();
		$data['failed_rescord'] = array();
		$dat_dup_array = array();

		//print_r($stu_data); die();
		$i=1;
		foreach($data['stu_data'] as $stu){
			if($i == 1){
				$i++;
				continue;	
			}
			
			if(array_key_exists($stu[1],$dat_dup_array) && $dat_dup_array[$stu[1]] == $stu[4]){
				$data['failed_rescord'] = $stu;
				
			}else{
				$dat_dup_array[$stu[1]] = $stu[4];
				$data['success_record'][] = $stu;
			}
			
			
			
			
		}
		
		
		
	//	print_r($data['success_record']);
	//	die();
		
		
		
		

		$this->load->view('_admin_shared/header',$data);	
		$this->load->view('_admin/stu_info',$data);
		$this->load->view('_admin_shared/footer',$data);

		

		
		
		
	}
	
	
	public function savetempresult(){
		$this->load->library('Admin_acess');
		$data["menu"] = "importresult";
		$data["resinfo"] = $_POST;
		if(empty($data["resinfo"])){
			redirect('admin/importresult');	
		}
		
		
		
		
		$target_dir = "assets/student_result_excel/";
			$result_title = $this->input->post('result_title',TRUE);
			$stream = $this->input->post('stream',TRUE);
			$semester = $this->input->post('semester',TRUE);
			$publish_date = $this->input->post('publish_date',TRUE);
			$result_type = $this->input->post('result_type',TRUE);
			$rechecking_allowed = $this->input->post('rechecking_allowed',TRUE);
			$task = $this->input->post('task',TRUE);
			$recid = $this->input->post('recid',TRUE);
			$auto_delete =  $this->input->post('hdn_auto_delete',TRUE);
			$import_only =  $this->input->post('hdn_import_only',TRUE);
			
			if(!empty($_POST['start_date'])){
				$start_date = $this->input->post('start_date',TRUE);
			}
			if(!empty($_POST['end_date'])){
				$end_date = $this->input->post('end_date',TRUE);
			}
			
			
			if(!empty($_FILES['importresult']['name'])){
				
				$newfilename= date('dmYHis').'_'.str_replace(" ", "", basename($_FILES["importresult"]["name"]));
				
				
				if(move_uploaded_file($_FILES["importresult"]["tmp_name"], $target_dir .$newfilename)){
					
					//echo 'success';
					//exit;
					
				}
				
			}
			
		$data['filename'] = $newfilename;
		$file_name = 'assets/student_result_excel/' . $newfilename;
		$file_type = \PhpOffice\PhpSpreadsheet\IOFactory::identify($file_name);
 		$reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($file_type);
		
 		$spreadsheet = $reader->load($file_name);
		$data['stu_data'] = $spreadsheet->getActiveSheet()->toArray();
		$data['success_record'] = array();
		$data['failed_record'] = array();
		$exist_in_db = array();
		$dat_dup_array = array();
		$data['total_record'] = 0;
		$data['streams'] = $this->config->item('streams');
		
		//die($import_only . ' -- ' . $recid);
		//check ony the new recorde will be shown
		//echo $import_only; die();
		if($import_only){
			$res_info = $this->adminModel->getAllSturegNoAndSubjectCode($recid);
			
			foreach($res_info as $val){
				$exist_in_db[$val->regd_no][] = $val->subject_code;
				$dat_dup_array[$val->regd_no][] = $val->subject_code;
				
			}
		}
		

		//print_r($dat_dup_array); die();
		$i=1;
		foreach($data['stu_data'] as $stu){
			
		if(!empty($stu[1])){	
					if($i == 1){
						$i++;
						continue;	
					}
					
					$data['total_record']++;
					
					if($task == 1){
					
						if(array_key_exists($stu[1],$dat_dup_array) && in_array($stu[4], $dat_dup_array[$stu[1]])){
							$data['failed_record'][] = array_merge($stu,array('Duplicate entry In excel'));
							
						}else{
							$dat_dup_array[$stu[1]][] = $stu[4];
							$data['success_record'][] = $stu;
						}
					}else{ 
						
						//$data['total_record']++;
						//Delete and reinsert all
						//if($auto_delete){
							
							
							//echo 
						
						if($auto_delete){ 
							
							
							if(array_key_exists($stu[1],$dat_dup_array) && in_array($stu[4], $dat_dup_array[$stu[1]])){		
									$data['failed_record'][] = array_merge($stu,array('Duplicate entry In excel'));
							}else{
								$dat_dup_array[$stu[1]][] = $stu[4];
								$data['success_record'][] = $stu;
							}
		
							
						}else{
						
							
							if(array_key_exists($stu[1],$dat_dup_array) &&  in_array($stu[4], $dat_dup_array[$stu[1]])){
								
								if(array_key_exists($stu[1],$exist_in_db) &&  in_array($stu[4], $exist_in_db[$stu[1]])){
									$data['failed_record'][] = array_merge($stu,array('Exist in database'));
								}else{
									$data['failed_record'][] = array_merge($stu,array('Duplicate entry In excel'));
								}
								
							}else{
								$dat_dup_array[$stu[1]][] = $stu[4];
								$data['success_record'][] = $stu;
							}
							
							
						}
							
							
							//print_r($dat_dup_array); die();
							
					
						//}
						
						
					}
					
				}
		}
		
		
		
		//print_r($data['success_record']);
		//print_r($data['failed_record']);
		//die();
		
		
		
		

		$this->load->view('_admin_shared/header',$data);	
		$this->load->view('_admin/stu_info',$data);
		$this->load->view('_admin_shared/footer',$data);
		
	}
	
	
	
	
	public function saveresultset(){ 
		$this->load->library('Admin_acess');
		$data["menu"] = "importresult";
		$streams = $this->config->item('streams');
		$title = $this->input->post('result_title',TRUE);
		$stream = $this->input->post('stream',TRUE);
		$stream = $streams[$stream][0];
		$semester = $this->input->post('semester',TRUE);
		$publish_date = $this->input->post('publish_date',TRUE);
		$result_type = $this->input->post('result_type',TRUE);
		$rechecking_allowed = $this->input->post('rechecking_allowed',TRUE);
		$filename = $this->input->post('filename',TRUE);
		
		$task = $this->input->post('task',TRUE);
		$recid = $this->input->post('recid',TRUE);
		$auto_delete = !(empty($_POST['hdn_auto_delete'])?1:0);
		$import_only = !(empty($_POST['hdn_import_only'])?1:0);

		
		$start_date = '';
		$end_date = '';
		if($rechecking_allowed){
			if(!empty($_POST['start_date'])){
				$start_date = date('Y-m-d',strtotime($_POST['start_date']));
			}
			if(!empty($_POST['end_date'])){
				$end_date = date('Y-m-d',strtotime($_POST['end_date']));
			}
			
		}
		//echo $start_date . ' -- ' . $end_date; die();
		
		
		$task = $this->input->post('task',TRUE);
		$recid = $this->input->post('recid',TRUE);
		
		$exist_in_db = array();
		$dat_dup_array = array();
		$total_reg_no = array();
		
		/*$total_stu = $this->adminModel->getAllRegNo();
		foreach($total_stu as $streg){
			$total_reg_no[] = $streg->regd_no;
		}*/
		
		//print_r($total_reg_no); die();
		
		
		if($import_only){
			$res_info = $this->adminModel->getAllSturegNoAndSubjectCode($recid);
			
			foreach($res_info as $val){
				$exist_in_db[$val->regd_no][] = $val->subject_code;
				$dat_dup_array[$val->regd_no][] = $val->subject_code;
				
			}
		}
		
		
		
		
		
		
		$file_name = 'assets/student_result_excel/'. $filename;
		$file_type = \PhpOffice\PhpSpreadsheet\IOFactory::identify($file_name);
 		$reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($file_type);

 		$spreadsheet = $reader->load($file_name);
		
		$stu_data = $spreadsheet->getActiveSheet()->toArray();
		$str_result = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'; 
		$length_of_string = 8;
		$pwd = '';
		$i=1;
		
		$this->db->trans_strict(TRUE);	
		$this->db->trans_begin();
		
		if($task == 1){

						$res_dt = array('title' => addslashes($title),
								'stream' => $stream,
								'semester' => $semester,
								'publish_date' => date('Y-m-d' ,strtotime($publish_date)),
								'rechecking_allowed' => $rechecking_allowed,
								'start_date' => !empty($start_date)?$start_date:NULL,
								'end_date' => !empty($end_date)?$end_date:NULL,
								'result_type' => $result_type);
						
						
						$result_id = $this->adminModel->addResult($res_dt);		
				
						//print_r($stu_data); die();
						foreach($stu_data as $dt){
							if(!empty($dt[1])){	
										if($i == 1){
											$i++;
											continue;	
										}
										$pwd = substr(str_shuffle($str_result),  0, $length_of_string);
									
										if(!$this->adminModel->checkRegNoinDB($dt[1])){
										//if(!in_array($dt[1] , $total_reg_no)){	
											
											
												$ins_stu = array('regd_no' => $dt[1] ,
																 'name' => $dt[2],
																 'email' => $dt[3],
																 'password' => $this->encryption->encrypt($pwd),
																 'status' => '1');	
																 
												$this->adminModel->addStudent($ins_stu);
											
										
										
										}
										
												$result_dt_details = array('result_id' => $result_id,
																		   'regd_no' => $dt[1],
																		   'branch' =>  $dt[0],
																		   'subject_code' => $dt[4] ,
																		   'subject_name' => $dt[5] ,
																		   'credits' => $dt[6],
																		   'grade' => $dt[7], 
																		   'sgpa' => $dt[8],
																		   'subject_type' => $dt[9]);
												
												$this->adminModel->addResultDetails($result_dt_details);
										
							}
					
						}
					
						
		}
		else{
			
			$res_dt = array('title' => addslashes($title),
							'stream' => $stream,
							'semester' => $semester,
							'publish_date' => date('y-m-d' ,strtotime($publish_date)),
							'result_type' => $result_type);
						
						
			$this->adminModel->updateResult($res_dt,$recid);
			$result_id = $recid;
			if($auto_delete){	
				$this->adminModel->deleteResutDetailsByResId($recid);
			}
			
									
						foreach($stu_data as $dt){
						 if(!empty($dt[1])){	
									if($i == 1){
										$i++;
										continue;	
									}
									$pwd = substr(str_shuffle($str_result),  0, $length_of_string);
								
									if(!$this->adminModel->checkRegNoinDB($dt[1])){
									
									//if(!in_array($dt[1] , $total_reg_no)){	
										$ins_stu = array('regd_no' => $dt[1] ,
														 'name' => $dt[2],
														 'email' => $dt[3],
														 'password' => $this->encryption->encrypt($pwd),
														 'status' => '1');	
														 
										$this->adminModel->addStudent($ins_stu);			 
									}
									
										
									
									if($auto_delete){	
									
											//$this->adminModel->deleteResutDetailsByResId($recid);	
									
											$result_dt_details = array('result_id' => $result_id,
																	   'regd_no' => $dt[1],
																	   'branch' =>  $dt[0],
																	   'subject_code' => $dt[4] ,
																	   'subject_name' => $dt[5] ,
																	   'credits' => $dt[6],
																	   'grade' => $dt[7], 
																	   'sgpa' => $dt[8],
																	   'subject_type' => $dt[9]);
											
											$this->adminModel->addResultDetails($result_dt_details);
											
									}
									
									
									
									if($import_only){
										
										if(array_key_exists($dt[1],$dat_dup_array) &&  in_array($dt[4], $dat_dup_array[$dt[1]])){
											
											//do Nothing
										}else{
											
											$result_dt_details = array('result_id' => $result_id,
																	   'regd_no' => $dt[1],
																	   'branch' =>  $dt[0],
																	   'subject_code' => $dt[4] ,
																	   'subject_name' => $dt[5] ,
																	   'credits' => $dt[6],
																	   'grade' => $dt[7], 
																	   'sgpa' => $dt[8],
																	   'subject_type' => $dt[9]);
											
											$this->adminModel->addResultDetails($result_dt_details);
									
										}
										
										
										
									}
									
									
						
						}		

						}
			
			
			
		}
		
		
		
		
		if($this->db->trans_status() === TRUE){
			$this->db->trans_commit(); 
			$result[] = array('resSuccess' => 1, 'msg' => 'Result saved successfully');
			echo json_encode($result);
			exit();				
			
		}else{
			 $this->db->trans_rollback(); 
			$result[] = array('resSuccess' => 2, 'msg' => 'Unable to save Result');
			echo json_encode($result);			
			exit();
		}	

		
		
		
		
		
		
	}
	
	
	public function resultset(){
		$this->load->library('Admin_acess');
		$data["menu"] = "viewresult";
		$data["results"] = $this->adminModel->resultset();
		//print_r($data["results"]); die();
		
		$this->load->view('_admin_shared/header',$data);	
		$this->load->view('_admin/result_set_list',$data);
		$this->load->view('_admin_shared/footer',$data);
		
	}
	
	
	public function detailresult($id){
		$this->load->library('Admin_acess');
		$data["menu"] = "viewresult";
		$data["detailres"] = $this->adminModel->detailRes($id);
		
		$this->load->view('_admin_shared/header',$data);	
		$this->load->view('_admin/detail_res_lst',$data);
		$this->load->view('_admin_shared/footer',$data);

	}
	
	
	
	public function students(){
		$this->load->library('Admin_acess');
		$data["menu"] = "students";
		
		$data['students'] = $this->adminModel->getStudentList();
	
		$this->load->view('_admin_shared/header',$data);	
		$this->load->view('_admin/student_list',$data);
		$this->load->view('_admin_shared/footer',$data);
		
		
		
	}
	
	
	
	public function deleteset(){ 
		$this->load->library('Admin_acess');
		$data["menu"] = "viewresult";
		$id = $this->input->post('id',TRUE);
		
		
		$this->db->trans_strict(TRUE);	
		$this->db->trans_begin();
		
			$this->adminModel->deleteResultDetails($id);
			$this->adminModel->deleteSet($id);


		if($this->db->trans_status() === TRUE){
			$this->db->trans_commit(); 
			$result[] = array('resSuccess' => 1, 'msg' => 'Set deleted successfully');
			echo json_encode($result);
			exit();				
			
		}else{
			 $this->db->trans_rollback(); 
			$result[] = array('resSuccess' => 2, 'msg' => 'Unable to delete Set');
			echo json_encode($result);			
			exit();
		}	

		
		
	}
	
	
	public function stugroup($id){
		
		$this->load->library('Admin_acess');
		$data["menu"] = "viewresult";
		
		$data['resultset'] = $this->adminModel->getResultSetById($id);
		$data['students'] = $this->adminModel->getStudentByResultId($id);
		
		$this->load->view('_admin_shared/header',$data);	
		$this->load->view('_admin/stu_group',$data);
		$this->load->view('_admin_shared/footer',$data);
		
	}
	
	
	public function deletesturesultset(){
		$this->load->library('Admin_acess');
		$data["menu"] = "viewresult";
		
		$this->db->trans_strict(TRUE);	
		$this->db->trans_begin();
		
		
		$result_id = $this->input->post('result_id',TRUE);
		$regd_no = $this->input->post('regd_no',TRUE);
		
			$this->adminModel->deleteStuResultSet($id);


		if($this->db->trans_status() === TRUE){
			$this->db->trans_commit(); 
			$result[] = array('resSuccess' => 1, 'msg' => 'Set deleted successfully');
			echo json_encode($result);
			exit();				
			
		}else{
			 $this->db->trans_rollback(); 
			$result[] = array('resSuccess' => 2, 'msg' => 'Unable to delete Set');
			echo json_encode($result);			
			exit();
		}	
		
	}
	
	
	public function viewresult($reg_no,$result_id){
		
		$this->load->library('Admin_acess');
		$data["menu"] = "viewresult";
		
		$data["result"] = $this->adminModel->getStuResult($reg_no,$result_id);
		$this->load->view('_admin/result',$data);
		
	}
	
	
	public function updateResult(){
		$this->load->library('Admin_acess');
		$data["menu"] = "importresult";
		
		$streams = $this->config->item('streams');
		
		$result_title = $this->input->post('result_title',TRUE);
		$stream = $this->input->post('stream',TRUE);
		$stream = $streams[$stream][0];
		$semester = $this->input->post('semester',TRUE);
		
		
		
		$publish_date = $this->input->post('publish_date',TRUE);
		$result_type =  $this->input->post('result_type',TRUE);
		$rechecking_allowed = $this->input->post('rechecking_allowed',TRUE);
		$start_date = '';
		$end_date = '';
		if($rechecking_allowed){
			
			if(!empty($_POST['start_date'])){
				$start_date = date('Y-m-d',strtotime($_POST['start_date']));
			}
			
			if(!empty($_POST['end_date'])){
				$end_date = date('Y-m-d',strtotime($_POST['end_date']));
			}
			
			
			
		}
		
		$recid = $this->input->post('recid',TRUE);
		$task  = $this->input->post('task',TRUE);
		
		
		$res_dt = array('title' => addslashes($result_title),
					  'stream' => $stream,
					  'semester' => $semester,
					  'publish_date' =>  date('Y-m-d',strtotime($publish_date)),
					  'rechecking_allowed' => $rechecking_allowed,
					  'start_date' => !empty($start_date)?$start_date:NULL,
					  'end_date' => !empty($end_date)?$end_date:NULL);
		
		
		$this->adminModel->updateResult($res_dt,$recid);
		
		if($this->db->trans_status() === TRUE){
			$this->db->trans_commit(); 
			$result[] = array('resSuccess' => 1, 'msg' => 'Result saved successfully');
			echo json_encode($result);
			exit();				
			
		}else{
			 $this->db->trans_rollback(); 
			$result[] = array('resSuccess' => 2, 'msg' => 'Unable to save result');
			echo json_encode($result);			
			exit();
		}	
		
		
		
		
		
	}
	
	
	
	

}
