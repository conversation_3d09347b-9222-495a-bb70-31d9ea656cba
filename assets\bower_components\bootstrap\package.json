{"name": "bootstrap", "description": "The most popular front-end framework for developing responsive, mobile first projects on the web.", "version": "3.4.1", "keywords": ["css", "less", "mobile-first", "responsive", "front-end", "framework", "web"], "homepage": "https://getbootstrap.com/", "author": "Twitter, Inc.", "scripts": {"sri": "node grunt/generate-sri.js", "release": "grunt prep-release && npm run sri && npm run release-zip", "release-zip": "cross-env-shell \"shx rm -rf bootstrap-$npm_package_version-dist && shx cp -r dist/ bootstrap-$npm_package_version-dist && zip -r9 bootstrap-$npm_package_version-dist.zip bootstrap-$npm_package_version-dist && shx rm -rf bootstrap-$npm_package_version-dist\"", "change-version": "node grunt/change-version.js", "test": "grunt test"}, "style": "dist/css/bootstrap.css", "less": "less/bootstrap.less", "main": "./dist/js/npm", "repository": {"type": "git", "url": "https://github.com/twbs/bootstrap.git"}, "bugs": {"url": "https://github.com/twbs/bootstrap/issues"}, "license": "MIT", "dependencies": {}, "devDependencies": {"autoprefixer": "^9.4.7", "btoa": "^1.2.1", "cross-env": "^5.2.0", "glob": "^7.1.3", "grunt": "^1.0.3", "grunt-contrib-clean": "^2.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-connect": "^2.0.0", "grunt-contrib-copy": "^1.0.0", "grunt-contrib-cssmin": "^3.0.0", "grunt-contrib-jshint": "^2.0.0", "grunt-contrib-less": "^2.0.0", "grunt-contrib-pug": "^2.0.0", "grunt-contrib-uglify": "^4.0.0", "grunt-contrib-watch": "^1.1.0", "grunt-exec": "^3.0.0", "grunt-html": "^10.1.0", "grunt-jekyll": "^1.0.0", "grunt-jscs": "^3.0.1", "grunt-postcss": "^0.9.0", "grunt-stylelint": "^0.10.1", "ip": "^1.1.5", "karma": "^3.1.3", "karma-browserstack-launcher": "^1.4.0", "karma-chrome-launcher": "^2.2.0", "karma-detect-browsers": "^2.3.3", "karma-firefox-launcher": "^1.1.0", "karma-qunit": "^1.2.1", "load-grunt-tasks": "^4.0.0", "markdown-it": "^8.4.2", "qunitjs": "^1.23.1", "replace-in-file": "^3.4.3", "shelljs": "^0.8.3", "shx": "^0.3.2", "stylelint": "~9.6.0", "stylelint-config-standard": "^18.2.0", "stylelint-order": "^1.0.0", "time-grunt": "^2.0.0"}, "engines": {"node": ">=6"}, "files": ["dist", "fonts", "grunt", "js/*.js", "less/**/*.less", "Gruntfile.js", "LICENSE"], "jspm": {"main": "js/bootstrap", "shim": {"js/bootstrap": {"deps": "j<PERSON>y", "exports": "$"}}, "files": ["css", "fonts", "js"]}}