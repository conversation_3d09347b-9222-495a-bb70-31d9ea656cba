
  <footer class="main-footer">
  <p><?php //echo $this->router->fetch_method();?></p>
    <strong>Copyright &copy; <?php echo date('Y'); ?> <a href="javascript:void(0);"><?php echo APP_TITLE ?></a>.</strong> All rights reserved.
  </footer>


</div>
<!-- ./wrapper -->

<!-- jQuery 3 -->
<script src="<?php echo base_url('assets/bower_components/jquery/dist/jquery.min.js') ?>"></script>
<!-- Bootstrap 3.3.7 -->
<script src="<?php echo base_url('assets/bower_components/bootstrap/dist/js/bootstrap.min.js') ?>"></script>
<!-- SlimScroll -->
<script src="<?php echo base_url('assets/bower_components/jquery-slimscroll/jquery.slimscroll.min.js') ?>"></script>
<!-- FastClick -->
<script src="<?php echo base_url('assets/bower_components/fastclick/lib/fastclick.js') ?>"></script>
<!-- AdminLTE App -->
<script src="<?php echo base_url('assets/dist/js/adminlte.min.js') ?>"></script>
<!-- AdminLTE for demo purposes -->
<script src="<?php echo base_url('assets/dist/js/demo.js')?>"></script>

<!-- DataTables -->
<link rel="stylesheet" href="<?php echo base_url('assets/bower_components/datatables.net-bs/css/dataTables.bootstrap.min.css')?>">
<!-- DataTables -->
<script src="<?php echo base_url('assets/bower_components/datatables.net/js/jquery.dataTables.min.js')?>"></script>
<script src="<?php echo base_url('assets/bower_components/datatables.net-bs/js/dataTables.bootstrap.min.js')?>"></script>

<!-- Validation Plugin Js -->
<script src="<?php echo base_url('assets/plugins/jquery-validation/jquery.validate.js') ?>"></script>

<!-- iCheck for checkboxes and radio inputs -->
<link rel="stylesheet" href="<?php echo base_url('assets/plugins/iCheck/all.css') ?>">
<!-- iCheck 1.0.1 -->
<script src="<?php echo base_url('assets/plugins/iCheck/icheck.min.js') ?>"></script>
<script src="<?php echo base_url('assets/plugins/bootbox/bootbox.all.min.js') ?>"></script>

<script>
<?php if($this->router->fetch_method() == 'categorylist'){?>
	$(document).ready(function(e) {
       
	   	//Category Datatable
		 $('#example1').DataTable({
			"columnDefs": [
				 { "width": "85%", "targets": 0 },
				 { "width": "15%",  "targets": 1 }
			],
			'paging'      : true,
			'searching'   : true,
			'ordering'    : true,
			'info'        : true,
			'autoWidth'   : false,
			"bLengthChange": false,
			"order": []
		
		})
		
		
		
		$('#modal-category').on('show.bs.modal', function () { 
				$('#cat_name').focus();
		});
	
		 
		 
		$('#modal-category').on('hidden.bs.modal', function () {
			$('#cat_name').parent('.form-group').removeClass('has-error');
			$('.cat-help').hide();
			$('#frmcategory #cat_name').val('');
			$('#frmcategory #task').val(1);
			$('#frmcategory #recid').val(0);
			$('.modal-title').html('Add Category');
			$('#cat_name').parent('.form-group').removeClass('has-error');
		});
		
		
		
	$('#frmcategory').validate({
			highlight: function (input) {
				$(input).parent('.form-group').addClass('has-error');
			},
			unhighlight: function (input) {
				$(input).parent('.form-group').removeClass('has-error');
			},
			errorPlacement: function (error, element) {
				//$(element).parents('.form-group').append(error);
			}
	});
	
	$('#frmcategory').submit(function(e) {
        
		e.preventDefault();
		if($('#frmcategory').valid()) { 
				$.ajax({
				type: "POST",
				url: "<?php echo base_url('admin/savecategory') ?>",
				data: $("#frmcategory").serialize(),
				dataType: 'json',
				beforeSend: function (xhr) {
				  $("#btnsave").prop("disabled",true);
				  $("#btnsave").html('<i class="fa fa-spinner fa-spin"></i> Saving...');
				  
				},
				success: function (res_data) {
					if (res_data[0].resSuccess == 1) { 
					  //$("#sign_btn").prop("disabled",false);
					  		$('#modal-category').modal('hide');
							bootbox.alert({ 
								size: "small",
								message: "Category saved successfully",
								callback:function(){
										window.location.href=window.location.href;
									}
										
								});	
					}else if (res_data[0].resSuccess == 2){
						  $("#btnsave").prop("disabled",false);
						   $("#btnsave").html('Save Category');
						   bootbox.alert({ 
								size: "small",
								message: res_data[0].msg,
								callback:function(){
										//window.location.href=window.location.href;
									}
							});	
				
							return false;
						}					
					}
				});
			}
		return false;
		});
	   
	
	});//document ready ends here
	
	
	
	function editCategory(id){
		var cat_name = $('.catname_' + id).html();	
		$('#modal-category').modal('show');
		$('.modal-title').html('Edit Category');
		$('#frmcategory #cat_name').val(cat_name);
		$('#frmcategory #task').val(2);	
		$('#frmcategory #recid').val(id);	
		
	}
	
	function deleteCategory(cat_id){
		bootbox.confirm({ 
			size: "small",
			message: "Are you sure? You want to delete this.",
			callback: function(result){ /* result is a boolean; true = OK, false = Cancel*/
					if(result){
							$.ajax({
								type: "POST",
								url: "<?php echo base_url('admin/deletecategory') ?>",
								data: {'cat_id':cat_id},
								dataType: 'json',
								beforeSend: function (xhr) {
								  $(".btn_" + cat_id).prop("disabled",true);
								  $(".btn_" + cat_id).html('<i class="fa fa-spinner fa-spin"></i>');
								  
								},
								success: function (res_data) {
									  $(".btn_" + cat_id).prop("disabled",false);
									  $(".btn_" + cat_id).html('<i class="fa fa-trash"></i>');
									
									if (res_data[0].resSuccess == 1) { 
									
										bootbox.alert({ 
											size: "small",
											message: "Category deleted successfully.",
											callback:function(){
													window.location.href=window.location.href;
												}
													
											});	
									
									}else if (res_data[0].resSuccess == 2){
										
											 bootbox.alert({ 
												size: "small",
												message: res_data[0].msg});	
											}					
									}
								});
						
					}
			 }
		})
		
	}	
	
	
	

<?php } ?>


<?php if($this->router->fetch_method() == 'instructorlist'){?>

	$(document).ready(function(e) {
		
		$('#tbl_instructor').DataTable({
			"columnDefs": [
				 { "width": "20%", "targets": 0 },
				 { "width": "65%",  "targets": 1 },
				 { "width": "15%", "targets": 2 },
			],
			'paging'      : true,
			'searching'   : true,
			'ordering'    : true,
			'info'        : true,
			'autoWidth'   : false,
			"bLengthChange": false,
			"order": []
		
		})		
        
	$('#frmInstructor').validate({
			highlight: function (input) {
				$(input).parent('.form-group').addClass('has-error');
			},
			unhighlight: function (input) {
				$(input).parent('.form-group').removeClass('has-error');
			},
			errorPlacement: function (error, element) {
				//$(element).parents('.form-group').append(error);
			}
	});
	
	$('#frmInstructor').submit(function(e) {
        
		e.preventDefault();
		if($('#frmInstructor').valid()) { 
				$.ajax({
				type: "POST",
				url: "<?php echo base_url('admin/saveinstructor') ?>",
				data: $("#frmInstructor").serialize(),
				dataType: 'json',
				beforeSend: function (xhr) {
				  $("#btnsave").prop("disabled",true);
				  $("#btnsave").html('<i class="fa fa-spinner fa-spin"></i> Saving...');
				  
				},
				success: function (res_data) {
					if (res_data[0].resSuccess == 1) { 
					
							$('#modal-instructor').modal('hide');
					  
							bootbox.alert({ 
								size: "small",
								message: "Instructor saved successfully.",
								callback:function(){
										window.location.href=window.location.href;
									}
										
								});	
					
					}else if (res_data[0].resSuccess == 2){
						  $("#btnsave").prop("disabled",false);
						   $("#btnsave").html('Save Instructor');
						   
							   bootbox.alert({ 
									size: "small",
									message: res_data[0].msg,
									callback:function(){
											//window.location.href=window.location.href;
										}
								});	
						   
					
							return false;
						}					
					}
				});
			}
		return false;
		});  
		
		
	$('#modal-instructor').on('hidden.bs.modal', function () {
		$('.modal-title').html('Add Instructor');
		$('#frmInstructor #instructor_name').val('');
		$('#frmInstructor #instructor_desc').val('');
		$('#frmInstructor #recid').val(0);	
		$('#frmInstructor #task').val(1);	
	
	});
		
  
  
  
    });//document ready ends here

	function editInstructor(id){
		
		
		$.ajax({
			type: "POST",
			url: "<?php echo base_url('admin/editinstructor') ?>",
			data: {'id':id},
			dataType: 'json',
			beforeSend: function (xhr) {
			  $(".edit_btn_" + id).prop("disabled",true);
			  $(".edit_btn_" + id).html('<i class="fa fa-spinner fa-spin"></i>');
			  
			},
			success: function (res_data) {
				//alert(res_data.instructor_name)
				
				$('#modal-instructor').modal('show', {backdrop: 'static'});
				$('#modal-instructor .modal-title').html('Edit Instructor');
				  	$(".edit_btn_" + id).prop("disabled",true);
				  	$(".edit_btn_" + id).html('<i class="fa fa-edit"></i>');
					
					$('#frmInstructor #instructor_name').val(res_data.instructor_name);
					$('#frmInstructor #instructor_desc').val(res_data.instructor_desc);
					$('#frmInstructor #task').val(2);
					$('#frmInstructor #recid').val(res_data.id);
				
			}
			});

	}
	
	
	
	function deleteInstructor(instructor_id){
		bootbox.confirm({ 
			size: "small",
			message: "Are you sure? You want to delete this.",
			callback: function(result){ /* result is a boolean; true = OK, false = Cancel*/
					if(result){
							$.ajax({
								type: "POST",
								url: "<?php echo base_url('admin/deleteinstructor') ?>",
								data: {'instructor_id':instructor_id},
								dataType: 'json',
								beforeSend: function (xhr) {
								  $(".btn_" + instructor_id).prop("disabled",true);
								  $(".btn_" + instructor_id).html('<i class="fa fa-spinner fa-spin"></i>');
								  
								},
								success: function (res_data) {
									  $(".btn_" + instructor_id).prop("disabled",false);
									  $(".btn_" + instructor_id).html('<i class="fa fa-trash"></i>');
									
									if (res_data[0].resSuccess == 1) { 

										
										bootbox.alert({ 
											size: "small",
											message: "Instructor deleted sucessfully.",
											callback:function(){
													window.location.href=window.location.href;
												}
													
											});	
									  
									}else if (res_data[0].resSuccess == 2){
										
											 bootbox.alert({ 
												size: "small",
												message: res_data[0].msg});	
											}					
									}
								});
						
					}
			 }
		})
		
	}	
	
	
<?php } ?>



<?php if($this->router->fetch_method() == 'videolist'){ ?>

	$(document).ready(function () {
		
		$('#tbl_videos').DataTable({
			"columnDefs": [
				 { "width": "30%", "targets": 0 },
				 { "width": "30%", "targets": 1 },
				 { "width": "25%", "targets": 2 },
				 { "width": "15%", "targets": 3 },
			],
			'paging'      : true,
			'searching'   : true,
			'ordering'    : true,
			'info'        : true,
			'autoWidth'   : false,
			"bLengthChange": false,
			"order": []
		
		})
		
		$('#showvideomodal').on('hidden.bs.modal', function () {
			$('#videomodalbox').html('<center><i class="fa fa-spinner fa-spin"></i><p>Please wait while we fetch your data...</p></center>');
			$('.modal-title').html('Videos');
		});
		
		
	});//document ready ends here
	
	
	function getVideos(video_id,video_title){
		var params = {
				"video_id" :video_id
		};
		$('#showvideomodal').modal('show', {backdrop: 'static'});
		$.get('<?php echo base_url("admin/getvideos") ?>', params, function (html) {
			$('#videomodalbox').html(html);
			$('.modal-title').html(video_title);
		}); 
		
	}
	
	function deletevideo(video_id){
		bootbox.confirm({ 
			size: "small",
			message: "Are you sure? You want to delete this.",
			callback: function(result){ /* result is a boolean; true = OK, false = Cancel*/
					if(result){
							$.ajax({
								type: "POST",
								url: "<?php echo base_url('admin/deletevideo') ?>",
								data: {'video_id':video_id},
								dataType: 'json',
								beforeSend: function (xhr) {
								  $(".btn_" + video_id).prop("disabled",true);
								  $(".btn_" + video_id).html('<i class="fa fa-spinner fa-spin"></i>');
								  
								},
								success: function (res_data) {
									if (res_data[0].resSuccess == 1) { 

										bootbox.alert({ 
											size: "small",
											message: "Video deleted sucessfully.",
											callback:function(){
													window.location.href=window.location.href;
												}
													
											});	
										
									  
									}else if (res_data[0].resSuccess == 2){
										
											 bootbox.alert({ 
												size: "small",
												message: "Unable to delete sucessfully"});	
											}					
									}
								});
						
					}
			 }
		})
		
	}		
	
	

<?php } ?>


<?php  if($this->router->fetch_method() == 'editvideos' ||  $this->router->fetch_method() == 'addvideos'){ ?>

	$(document).ready(function () {
		
		//iCheck for checkbox and radio inputs
		$('input[type="checkbox"].minimal, input[type="radio"].minimal').iCheck({
		  checkboxClass: 'icheckbox_minimal-blue',
		  radioClass   : 'iradio_minimal-blue'
		})
		
		
		$('#frmVideos').validate({
			
				rules: {
					"sub_title[]": "required"
				},
				rules: {
					"embed_video[]": "required"
				},
				ignore: [],
			
				highlight: function (input) {
					$(input).parent('.form-group').addClass('has-error');
				},
				unhighlight: function (input) {
					$(input).parent('.form-group').removeClass('has-error');
				},
				errorPlacement: function (error, element) {
					//$(element).parents('.form-group').append(error);
				}
		});	
		
		$('#frmVideos').submit(function(e) {
			
			
			e.preventDefault();
			if($('#frmVideos').valid()) { 
					$.ajax({
					type: "POST",
					url: "<?php echo base_url('admin/savevideos') ?>",
					data: $("#frmVideos").serialize(),
					dataType: 'json',
					beforeSend: function (xhr) {
					  $("#btnsave").prop("disabled",true);
					  $("#btnsave").html('<i class="fa fa-spinner fa-spin"></i> Saving and generating thumbnails...');
					  
					},
					success: function (res_data) {
						if (res_data[0].resSuccess == 1) { 
						
								bootbox.alert({ 
									size: "small",
									message: "Videos saved successfully.",
									callback:function(){
											window.location.href="<?php echo base_url('admin/videos') ?>";
										}
											
									});	
						
						}else if (res_data[0].resSuccess == 2){
							  $("#btnsave").prop("disabled",false);
							   $("#btnsave").html('Save Videos');
							 
								   bootbox.alert({ 
										size: "small",
										message: 'Unable to save videos',
										callback:function(){
												//window.location.href=window.location.href;
											}
									});	
						
								return false;
							}					
						}
					});
				}
			return false;
			});			
		
		
	});//document ready ends here
	
	
	function addMore(){
		var count = $('.multiple-videos').length;
		var params = {
				"count": count,
		};
		
		$.get('<?php echo base_url("admin/addmore") ?>', params, function (html) {
			$('.tttt').append(html);
			refreshradiobutton();
			addvalidation();
			
		}); 
	}	
	
	
	function refreshradiobutton(){
		$('input[type="checkbox"].minimal, input[type="radio"].minimal').iCheck({
		  checkboxClass: 'icheckbox_minimal-blue',
		  radioClass   : 'iradio_minimal-blue'
		})
		
	}
	
	function addvalidation(){
		
			$('[name*="sub_title"]').each(function () {  
				$(this).rules('add', {  
					required: true,  
					messages: {  
						required: "This field is required."  
					}  
				});  
			}); 
		
	}	
	
	
	//function editvideos($i)
	function remove(obj,id){
	
		$(obj).html('<i class="fa fa-spinner fa-spin"></i>');
		
		if(id){
				bootbox.confirm({ 
					size: "small",
					message: "Are you sure? You want to delete this.",
					callback: function(result){ /* result is a boolean; true = OK, false = Cancel*/
							if(result){
									$.ajax({
										type: "POST",
										url: "<?php echo base_url('admin/deletevideodetails') ?>",
										data: {'id':id},
										dataType: 'json',
										beforeSend: function (xhr) {
											
											$(obj).html('<i class="fa fa-spinner fa-spin"></i>');
										 	$(".btnsave").prop("disabled",true);
										
										  
										},
										success: function (res_data) {
											$(".btnsave").prop("disabled",false);
											if (res_data[0].resSuccess == 1) { 

											
													//console.log($(obj).closest('.multiple-videos').length);
													$(obj).closest('.multiple-videos').remove();
													var k=1;
													$(".multiple-videos").each(function(){
														$(this).find('input[type=radio]').attr('id','after_login_' + k);
														$(this).find('input[type=radio]').attr('name','after_login_' + k);
														
												
														$(this).find('input[name^="sub_title_"]').attr('id','sub_title_' + k);
														$(this).find('input[name^="sub_title_"]').attr('name','sub_title_' + k);
														
														$(this).find('input[name^="embed_video_"]').attr('id','embed_video_' + k);
														$(this).find('input[name^="embed_video_"]').attr('name','embed_video_' + k);
														
														k++;	
													});
													
													 bootbox.alert({ 
															size: "small",
															message: "Video deleted sucessfully"});	
																			
																						
											
											}else if (res_data[0].resSuccess == 2){
												
													 bootbox.alert({ 
														size: "small",
														message: "Unable to delete sucessfully"});	
													}					
											
											}
										
										});
								
							}else{
								
								$(obj).html('<i class="fa fa-remove"></i>');
								
							}
					 }
				})
				
			
		}else{
			
					//console.log($(obj).closest('.multiple-videos').length);
			$(obj).closest('.multiple-videos').remove();
			var k=1;
			$(".multiple-videos").each(function(){
				$(this).find('input[type=radio]').attr('id','after_login_' + k);
				$(this).find('input[type=radio]').attr('name','after_login_' + k);
				
		
				$(this).find('input[name^="sub_title_"]').attr('id','sub_title_' + k);
				$(this).find('input[name^="sub_title_"]').attr('name','sub_title_' + k);
				
				$(this).find('input[name^="embed_video_"]').attr('id','embed_video_' + k);
				$(this).find('input[name^="embed_video_"]').attr('name','embed_video_' + k);
				
				k++;	
			});
			
		}
		
		
		return false;
		
	}
	

<?php } ?>


<?php if($this->router->fetch_method() == 'userlist'){ ?>

	 $(document).ready(function () { 
		$('#tbl_users').DataTable({
			"columnDefs": [
				 { "width": "25%", "targets": 0 },
				 { "width": "25%", "targets": 1 },
				 { "width": "25%", "targets": 2 },
				 { "width": "25%", "targets": 3 },
			],
			'paging'      : true,
			'searching'   : true,
			'ordering'    : true,
			'info'        : true,
			'autoWidth'   : false,
			"bLengthChange": false,
			"order": []
		
		})
		
	 });

<?php } ?>

<?php if($this->router->fetch_method() == 'historylist'){ ?>

	 $(document).ready(function () {  
		$('#tbl_history').DataTable({
			"columnDefs": [
				 { "width": "20%", "targets": 0 },
				 { "width": "20%", "targets": 1 },
				 { "width": "20%", "targets": 2 },
				 { "width": "20%", "targets": 3 },
				 { "width": "20%", "targets": 3 },
			],
			'paging'      : true,
			'searching'   : true,
			'ordering'    : true,
			'info'        : true,
			'autoWidth'   : false,
			"bLengthChange": false,
			"order": []
		
		})
		
	 });

<?php } ?>

<?php if($this->router->fetch_method() == 'changepwd'){ ?>
		$(document).ready(function () {  
			
				$('#frmChangpwd').validate({
						highlight: function (input) {
							$(input).parent('.form-group').addClass('has-error');
						},
						unhighlight: function (input) {
							$(input).parent('.form-group').removeClass('has-error');
						},
						errorPlacement: function (error, element) {
							//$(element).parents('.form-group').append(error);
						}
				});
	
			$('#frmChangpwd').submit(function(e) {
				
				e.preventDefault();
				if($('#frmChangpwd').valid()) { 
				
						if($('#new_password').val() != $('#confirm_password').val()){
								bootbox.alert({ 
									size: "small",
									message: "Your password and confirmation password do not match."});	
								
								return false;				
						}
				
						$.ajax({
						type: "POST",
						url: "<?php echo base_url('admin/saveprofile') ?>",
						data: $("#frmChangpwd").serialize(),
						dataType: 'json',
						beforeSend: function (xhr) {
						  $("#btnsave").prop("disabled",true);
						  $("#btnsave").html('<i class="fa fa-spinner fa-spin"></i> Saving...');
						  
						},
						success: function (res_data) {
							if (res_data[0].resSuccess == 1) { 
							  //$("#sign_btn").prop("disabled",false);
									$('#modal-category').modal('hide');
									bootbox.alert({ 
										size: "small",
										message: "Profile saved successfully",
										callback:function(){
												window.location.href="<?php echo base_url('admin/logout'); ?>";
											}
												
										});	
							}else if (res_data[0].resSuccess == 2){
								  $("#btnsave").prop("disabled",false);
								   $("#btnsave").html('Save Category');
								   bootbox.alert({ 
										size: "small",
										message: res_data[0].msg,
										callback:function(){
												//window.location.href=window.location.href;
											}
									});	
						
									return false;
								}					
							}
						});
					}
				return false;
				});
		
		
		});

<?php } ?>

  $(document).ready(function () {
	  
    //$('.sidebar-menu').tree()
	
  });
  
  
</script>
</body>
</html>
