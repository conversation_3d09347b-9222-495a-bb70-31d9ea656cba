<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
class Admin_model extends CI_Model {

    function __construct()
    {
        // Call the Model constructor
        parent::__construct();
		
    }
	
	public function validate_login($userid,$password){
		
		$this->db->select('id,name,email,password');
		$this->db->from('tbl_admin');
		
		$this->db->where(array('email' => $userid));
        $query = $this->db->get();
		
		if($query->num_rows() > 0 ){
			$row = $query->row();
			if($this->encryption->decrypt($row->password) == $password){
				$this->session->set_userdata('sess_user_id',$row->id);
				$this->session->set_userdata('sess_user_name',ucfirst($row->name));
				$this->session->set_userdata('sess_user_email',$row->email);
				$this->session->set_userdata('sess_user_domain','');
				$this->session->set_userdata('sess_admin_login',1);	
				return true;
			}else{
				return false;	
			}
		}else{
			return false;	
		}
	}
	
	public function validatemail($email){
		
		$this->db->select('id');
		$this->db->from('tbl_admin');
		$this->db->where('email', $email);
		$query = $this->db->get();
		if(!empty($query->row())){
			return true;
		}else{
			return false;
		}

	}
	
	
	public function getAdmindetails($email=''){
		$this->db->select('password');
		$this->db->from('tbl_admin');
		$this->db->where(array('id' => $this->session->userdata('sess_user_id')));
        $query = $this->db->get();
		return $query->row();		
	}
	
	
	public function checkRegNoinDB($regno){
		
		$this->db->select('regd_no');
		$this->db->from('tbl_students');
		$this->db->where('regd_no',$regno);
        $query = $this->db->get();
		if(!empty($query->row())){
			return true;	
		}else{
			return false;	
		}
	}
	
	
	public function addStudent($ins_data){
		$this->db->insert('tbl_students', $ins_data);
		$insert_id = $this->db->insert_id();
		return ($this->db->affected_rows() != 1) ? false : $insert_id;
		
	}
	
	
	public function addResult($res_data){
		$this->db->insert('tbl_result', $res_data);
		$insert_id = $this->db->insert_id();
		return ($this->db->affected_rows() != 1) ? false : $insert_id;
	}
	
	
	public function addResultDetails($res_data){
		$this->db->insert('tbl_result_details', $res_data);
		$insert_id = $this->db->insert_id();
		return ($this->db->affected_rows() != 1) ? false : $insert_id;
		
	}
	
	
	public function getStudentList(){
		$this->db->select('regd_no,name,email,password,status');
		$this->db->from('tbl_students');
        $query = $this->db->get();
		return $query->result();		
		
	}
	
	public function getStudentByResultId($id){
		$this->db->select('ts.regd_no,ts.name,ts.email,ts.status');
		$this->db->from('tbl_students ts');
		$this->db->join('tbl_result_details trd','trd.regd_no = ts.regd_no');
		$this->db->where(array('md5(trd.result_id)' => $id));
		$this->db->group_by('ts.regd_no'); 
		//$this->db->where("EXISTS(SELECT regd_no FROM tbl_result_details trd where md5(trd.result_id) = '" . $id."')");
	    $query = $this->db->get();
		//echo $this->db->last_query(); die();
		
		return $query->result();
				
	}
	
	
	
	
	public function updateProfile($data){ 
		$id = $this->session->userdata('sess_user_id');
		$this->db->where(array('id' => $id));
		$this->db->update('tbl_admin', $data);
		//echo $this->db->last_query(); die();
		if($this->db->affected_rows() >=0){
			return true;	
		}else{
			return false;
		}

		
	}
	
	
	public function resultset(){
		
		$this->db->select('tr.id,tr.title,tr.stream,tr.semester,tr.publish_date,tr.rechecking_allowed,tr.start_date,tr.end_date,tr.result_type');
		$this->db->from('tbl_result tr');
		$query = $this->db->get();
		//echo $this->db->last_query(); die('xxx');
		return $query->result();
		
		
	}
	
	
	
	
	
	public function deleteFiles($path){
		$file = $path; 
		if(is_file($file)){
			unlink($file); 
		}
		return 1;   
	}
	
	public function getAdminUserDetails($adminemail){
		$this->db->order_by("ID", "ASC");
		$this->db->where(array('UserEmail' => $adminemail, 'Status' => 1));
        $query = $this->db->get("tbl_admin",1);
        return $query->row();
	}
	
	public function getAdminUserDetailsByEmail($adminemail){
		$this->db->select('UserName, UserEmail');
		$this->db->from('tbl_admin');
		$this->db->where(array('UserEmail' => $adminemail, 'Status' => 1));		
		$query = $this->db->get();		
		return $query->row();
	}
	
	
	public function getRegUsers(){
		$this->db->select('user_id, user_name, user_email, user_status');
		$this->db->from('tbl_users');
		$query = $this->db->get();		
		return $query->result();
		
	}
	
	public function getStatusById($userid){
		
		$this->db->select('user_status');
		$this->db->from('tbl_users');
		$this->db->where(array('user_id' => $userid));		
		$query = $this->db->get();		
		return $query->row();
		
	}
	
	public function updateStatusByID($userid,$status){
		$data = array(
			'user_status' => $status,
		);
		$this->db->where(array('user_id' => $userid));
		$this->db->update('tbl_users', $data);
		//echo $this->db->last_query(); die();
		if($this->db->affected_rows() >=0){
			return true;	
		}else{
			return false;
		}

	}
	
	
	
	public function deleteSet($id){
		
		$this->db->delete('tbl_result', array('id' => $id));
		return ($this->db->affected_rows() != 1) ? false : true;	
		
	}
	
	public function deleteResultDetails($id){
		$this->db->delete('tbl_result_details', array('result_id' => $id));
		return ($this->db->affected_rows() != 1) ? false : true;	
		
	}
	
	
	public function getResultSetById($id){
		
		$sql = 'SELECT id,title,stream,semester FROM tbl_result WHERE md5(id) = "' . $id . '"';
		//$this->db->select('tr.id,tr.title,tr.stream,tr.semester');
		//$this->db->from('tbl_result tr');
		//$this->db->where(array('md5(id)' => $id));
		//$this->db->where("EXISTS(SELECT regd_no FROM tbl_result_details trd where trd.result_id = tr.id)");
		$query = $this->db->query($sql);
		//echo $this->db->last_query(); die();
		return $query->row();
		
	}
	
	public function deleteStuResultSet($regd_no,$result_id){
		$this->db->delete('tbl_result_details', array('result_id' => $result_id,'regd_no' => $regd_no));
		return ($this->db->affected_rows() != 1) ? false : true;	
		
	}
	
	
	public function getStuResult($reg_no,$result_id){
		
		$this->db->select('tr.id,tr.title,tr.stream,tr.semester,tr.publish_date,tr.rechecking_allowed,tr.start_date,tr.end_date,tr.result_type,
							trd.branch, trd.subject_code, trd.subject_name, trd.credits, trd.grade, trd.sgpa, trd.subject_type,
							ts.regd_no, ts.name, ts.email');
		$this->db->from('tbl_result as tr');
		$this->db->join('tbl_result_details as trd','trd.result_id = tr.id');
		$this->db->join('tbl_students as ts','ts.regd_no = trd.regd_no');
		$this->db->where(array('trd.regd_no'=> $reg_no, 'md5(trd.result_id)' => $result_id));	
		$query = $this->db->get();
		//echo $this->db->last_query(); die();
		return $query->result();	
		
	}
	
	
	public function getResutByMd5OfresultId($res_id){
		$this->db->select('tr.id,tr.title,tr.stream,tr.semester,tr.publish_date,tr.rechecking_allowed,tr.start_date,tr.end_date,tr.result_type');
		$this->db->from('tbl_result as tr');
		$this->db->where(array('md5(tr.id)' => $res_id));	
		$query = $this->db->get();
		return $query->row();	
	}
	
	
	public function getResutByresultId($res_id){
		$this->db->select('tr.id,tr.title,tr.stream,tr.semester,tr.publish_date,tr.rechecking_allowed,tr.start_date,tr.end_date,tr.result_type');
		$this->db->from('tbl_result as tr');
		$this->db->where(array('tr.id' => $res_id));	
		$query = $this->db->get();
		return $query->row();	
	}
	
	
	public function getAllSturegNoAndSubjectCode($res_id){
		
		$this->db->select('regd_no,subject_code');
		$this->db->from('tbl_result_details');
		$this->db->where(array('result_id' => $res_id));
		$query = $this->db->get();
		return $query->result();
		
	}
	
	
	public function deleteResutDetailsByResId($res_id){
		$this->db->delete('tbl_result_details', array('result_id' => $res_id));
		return ($this->db->affected_rows() != 1) ? false : true;	

	}
	
	public function updateResult($data,$res_id){
		
		$this->db->where(array('id' => $res_id));
		$this->db->update('tbl_result', $data);
		//echo $this->db->last_query(); die();
		if($this->db->affected_rows() >=0){
			return true;	
		}else{
			return false;
		}
	}
	
	public function getAllRegNo(){
		
		$this->db->select('regd_no');
		$this->db->from('tbl_students');
		$query = $this->db->get();
		return $query->result();
	}
	
	public function detailRes($result_id){
		$this->db->select('regd_no,branch,subject_code,subject_name,credits,grade,sgpa,subject_type');
		$this->db->from('tbl_result_details');
		$this->db->where('result_id',$result_id);
		$query = $this->db->get();
		return $query->result();
	}
	
	
	
	public function getUserByEmail($email){
		$this->db->select('id,name,email');
		$this->db->from('tbl_admin');
		$this->db->where('email',$email);
		$query = $this->db->get();
		return $query->row();
		
	}
	
	
	public function getadmindetailsByMd5ID($id){
		$this->db->select('id,name,email');
		$this->db->from('tbl_admin');
		$this->db->where('md5(id)',$id);
		$query = $this->db->get();
		return $query->row();
	}
	
	
	
}