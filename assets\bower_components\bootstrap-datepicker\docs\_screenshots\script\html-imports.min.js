// Copyright (c) 2012 The Polymer Authors. All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//    * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//    * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

window.HTMLImports=window.HTMLImports||{flags:{}},function(a){var b=(a.path,a.xhr),c=function(a,b){this.cache={},this.onload=a,this.oncomplete=b,this.inflight=0,this.pending={}};c.prototype={addNodes:function(a){this.inflight+=a.length,d(a,this.require,this),this.checkDone()},require:function(a){var b=a.src||a.href;a.__nodeUrl=b,this.dedupe(b,a)||this.fetch(b,a)},dedupe:function(a,b){return this.pending[a]?(this.pending[a].push(b),!0):this.cache[a]?(this.onload(a,b,this.cache[a]),this.tail(),!0):(this.pending[a]=[b],!1)},fetch:function(a,c){var d=function(b,d){this.receive(a,c,b,d)}.bind(this);b.load(a,d)},receive:function(a,b,c,d){c||(this.cache[a]=d),this.pending[a].forEach(function(b){c||this.onload(a,b,d),this.tail()},this),this.pending[a]=null},tail:function(){--this.inflight,this.checkDone()},checkDone:function(){this.inflight||this.oncomplete()}},b=b||{async:!0,ok:function(a){return a.status>=200&&a.status<300||304===a.status||0===a.status},load:function(c,d,e){var f=new XMLHttpRequest;return(a.flags.debug||a.flags.bust)&&(c+="?"+Math.random()),f.open("GET",c,b.async),f.addEventListener("readystatechange",function(){4===f.readyState&&d.call(e,!b.ok(f)&&f,f.response||f.responseText,c)}),f.send(),f},loadDocument:function(a,b,c){this.load(a,b,c).responseType="document"}};var d=Array.prototype.forEach.call.bind(Array.prototype.forEach);a.xhr=b,a.Loader=c}(window.HTMLImports),function(a){function b(a){return c(a,j)}function c(a,b){return"link"===a.localName&&a.getAttribute("rel")===b}function d(a){return"script"===a.localName}function e(a,b){var c=a;c instanceof Document||(c=document.implementation.createHTMLDocument(j)),c._URL=b;var d=c.createElement("base");return d.setAttribute("href",b),c.baseURI||(c.baseURI=b),c.head.appendChild(d),a instanceof Document||(c.body.innerHTML=a),window.HTMLTemplateElement&&HTMLTemplateElement.bootstrap&&HTMLTemplateElement.bootstrap(c),c}function f(a,b){function c(){k==l&&requestAnimationFrame(a)}function d(){k++,c()}b=b||o;var e=HTMLImports.isIE?"complete":"interactive",h="complete"===b.readyState||b.readyState===e;if(!h){var i=function(){("complete"===b.readyState||b.readyState===e)&&(b.removeEventListener("readystatechange",i),f(a,b))};return void b.addEventListener("readystatechange",i)}var j=b.querySelectorAll("link[rel=import]"),k=0,l=j.length;if(l)for(var m,n=0;l>n&&(m=j[n]);n++)g(m)?d.call(m):(m.addEventListener("load",d),m.addEventListener("error",d));else c()}function g(a){return i?a.import&&"loading"!==a.import.readyState:a.__importParsed}var h="import"in document.createElement("link"),i=!a.flags.imports&&h,j="import";if(!i){{var k,l=a.Loader,m=(a.xhr,"stylesheet"),n={documents:{},cache:{},preloadSelectors:["link[rel="+j+"]","template","script[src]:not([type])",'script[src][type="text/javascript"]'].join(","),loader:function(a){if(k&&k.inflight){var b=k.oncomplete;return k.oncomplete=function(){b(),a()},k}return k=new l(n.loaded,a),k.cache=n.cache,k},load:function(a,b){k=n.loader(b),n.preload(a)},preload:function(a){var b=this.marshalNodes(a);k.addNodes(b)},marshalNodes:function(a){var b=a.querySelectorAll(n.preloadSelectors);return b=this.filterMainDocumentNodes(a,b),b=this.extractTemplateNodes(b)},filterMainDocumentNodes:function(a,b){return a===document&&(b=Array.prototype.filter.call(b,function(a){return!d(a)})),b},extractTemplateNodes:function(a){var b=[];return a=Array.prototype.filter.call(a,function(a){if("template"===a.localName){if(a.content){var c=a.content.querySelectorAll("link[rel="+m+"]");c.length&&(b=b.concat(Array.prototype.slice.call(c,0)))}return!1}return!0}),b.length&&(a=a.concat(b)),a},loaded:function(a,c,d){if(b(c)){var f=n.documents[a];f||(f=e(d,a),n.documents[a]=f,n.preload(f)),c.import=c.content=d=f}c.__resource=d}};Array.prototype.forEach.call.bind(Array.prototype.forEach)}a.importer=n}var o=window.ShadowDOMPolyfill?wrap(document):document;Object.defineProperty(o,"_currentScript",{get:function(){return HTMLImports.currentScript||o.currentScript},writeable:!0,configurable:!0}),a.hasNative=h,a.useNative=i,a.whenImportsReady=f,a.IMPORT_LINK_TYPE=j,a.isImportLoaded=g}(window.HTMLImports),function(a){function b(a){var b=a.ownerDocument.createElement("style");return b.textContent=a.textContent,g.resolveUrlsInStyle(b),b}function c(a,b){this.doc=a,this.doc.__loadTracker=this,this.callback=b}function d(a){return"link"===a.localName&&a.getAttribute("rel")===h}function e(a){return a.parentNode&&!f(a)}function f(a){return a.ownerDocument===document||a.ownerDocument.impl===document}var g=a.path,h="import",i=/Trident/.test(navigator.userAgent),j={selectors:["link[rel="+h+"]","link[rel=stylesheet]","style","script:not([type])",'script[type="text/javascript"]'],map:{link:"parseLink",script:"parseScript",style:"parseStyle"},parse:function(a,b){if(a.__importParsed)b&&b();else{a.__importParsed=!0;for(var d,e=new c(a,b),f=a.querySelectorAll(j.selectors),g=a.scripts?a.scripts.length:0,h=0;h<f.length&&(d=f[h]);h++)j[j.map[d.localName]](d),a.scripts&&g!==a.scripts.length&&(g=a.scripts.length,f=a.querySelectorAll(j.selectors));e.open()}},parseLink:function(a){if(d(a)){if(this.trackElement(a),a.__resource?j.parse(a.__resource,function(){a.dispatchEvent(new CustomEvent("load",{bubbles:!1}))}):a.dispatchEvent(new CustomEvent("error",{bubbles:!1})),a.__pending)for(var b;a.__pending.length;)b=a.__pending.shift(),b&&b({target:a});a.__importParsed=!0}else e(a)&&(a.href=a.href),this.parseGeneric(a)},trackElement:function(a){i&&"style"===a.localName||a.ownerDocument.__loadTracker.require(a)},parseStyle:function(a){a=e(a)?b(a):a,this.parseGeneric(a)},parseGeneric:function(a){f(a)||(this.trackElement(a),document.head.appendChild(a))},parseScript:function(b){if(e(b)){var c=(b.__resource||b.textContent).trim();if(c){var d=b.__nodeUrl;if(!d){d=b.ownerDocument.baseURI;var f="["+Math.floor(1e3*(Math.random()+1))+"]",g=c.match(/Polymer\(['"]([^'"]*)/);f=g&&g[1]||f,d+="/"+f+".js"}c+="\n//# sourceURL="+d+"\n",a.currentScript=b,eval.call(window,c),a.currentScript=null}}}},k=/(url\()([^)]*)(\))/g,l=/(@import[\s]*)([^;]*)(;)/g,g={resolveUrlsInStyle:function(a){var b=a.ownerDocument,c=b.createElement("a");return a.textContent=this.resolveUrlsInCssText(a.textContent,c),a},resolveUrlsInCssText:function(a,b){var c=this.replaceUrls(a,b,k);return c=this.replaceUrls(c,b,l)},replaceUrls:function(a,b,c){return a.replace(c,function(a,c,d,e){var f=d.replace(/["']/g,"");return b.href=f,f=b.href,c+"'"+f+"'"+e})}};c.prototype={pending:0,isOpen:!1,open:function(){this.isOpen=!0,this.checkDone()},add:function(){this.pending++},require:function(a){this.add();var b=this,c=function(a){b.receive(a)};if(d(a))a.__pending=a.__pending||[],a.__pending.push(c);else for(var e,f=["load","error"],g=0,h=f.length;h>g&&(e=f[g]);g++)a.addEventListener(e,c)},receive:function(){this.pending--,this.checkDone()},checkDone:function(){this.isOpen&&this.pending<=0&&this.callback&&(this.isOpen=!1,this.callback())}};Array.prototype.forEach.call.bind(Array.prototype.forEach);a.parser=j,a.path=g,a.isIE=i}(HTMLImports),function(){function a(){HTMLImports.ready=!0,HTMLImports.readyTime=(new Date).getTime(),c.dispatchEvent(new CustomEvent("HTMLImportsLoaded",{bubbles:!0}))}function b(){HTMLImports.useNative||HTMLImports.importer.load(c,function(){HTMLImports.parser.parse(c)})}"function"!=typeof window.CustomEvent&&(window.CustomEvent=function(a,b){var c=document.createEvent("HTMLEvents");return c.initEvent(a,b.bubbles===!1?!1:!0,b.cancelable===!1?!1:!0,b.detail),c});var c=window.ShadowDOMPolyfill?window.ShadowDOMPolyfill.wrapIfNeeded(document):document;HTMLImports.useNative||("complete"===document.readyState||"interactive"===document.readyState&&!window.attachEvent?b():document.addEventListener("DOMContentLoaded",b)),HTMLImports.whenImportsReady(function(){a()})}();
//# sourceMappingURL=html-imports.min.map