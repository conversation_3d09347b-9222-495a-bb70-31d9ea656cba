<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <title><?php echo APP_STUDENT_TITLE ?> | Student Log in</title>
  <!-- Tell the browser to be responsive to screen width -->
  <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
  <!-- Bootstrap 3.3.7 -->
  <link rel="stylesheet" href="<?php echo base_url('assets/bower_components/bootstrap/dist/css/bootstrap.min.css') ?>">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="<?php echo base_url('assets/bower_components/font-awesome/css/font-awesome.min.css') ?>">
  <!-- Ionicons -->
  <link rel="stylesheet" href="<?php echo base_url('assets/bower_components/Ionicons/css/ionicons.min.css') ?>">
  <!-- Theme style -->
  <link rel="stylesheet" href="<?php echo base_url('assets/dist/css/AdminLTE.min.css') ?>">

  <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
  <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
  <!--[if lt IE 9]>
  <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
  <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
  <![endif]-->

  <!-- Google Font -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,600,700,300italic,400italic,600italic">
</head>
<body class="hold-transition login-page">
<div class="login-box">
  <div class="login-logo">
    <a href="<?php echo base_url('admin') ?>"><b><?php echo APP_STUDENT_TITLE ?></b></a>
  </div>
  <!-- /.login-logo -->
  <div class="login-box-body">
    <p class="login-box-msg">Sign in to start your session</p>

    <form id="sign_in" method="POST" novalidate>
      <div class="form-group has-feedback">
        <input type="text" id="regdno" name="regdno" autocomplete="off" class="form-control" placeholder="Regdno" required>
        <span class="glyphicon glyphicon-envelope form-control-feedback"></span>
      </div>
      <div class="form-group has-feedback">
        <input type="password" id="password" name="password" autocomplete="off" class="form-control" placeholder="Password" required>
        <span class="glyphicon glyphicon-lock form-control-feedback"></span>
      </div>
      <div class="row">
          <div class="col-sm-8">
          	&nbsp;
          </div>
        <!-- /.col -->
        <div class="col-xs-4">
          <button type="submit" class="btn btn-primary btn-block btn-flat">Sign In</button>
        </div>
        <!-- /.col -->
      </div>
    </form>


  </div>
  <!-- /.login-box-body -->
</div>
<!-- /.login-box -->

<!-- jQuery 3 -->
<script src="<?php echo base_url('assets/bower_components/jquery/dist/jquery.min.js') ?>"></script>
<!-- Bootstrap 3.3.7 -->
<script src="<?php echo base_url('assets/bower_components/bootstrap/dist/js/bootstrap.min.js') ?>"></script>
<!-- Validation Plugin Js -->
<script src="<?php echo base_url('assets/plugins/jquery-validation/jquery.validate.js') ?>"></script>
<script src="<?php echo base_url('assets/plugins/VanillaToasts-master/vanillatoasts.js') ?>"></script>
<link href="<?php echo base_url('assets/plugins/VanillaToasts-master/vanillatoasts.css') ?>" rel="stylesheet"></script>

<script>
    
$(function () {
//creteToast('Login Error','Unauthenciated user','error');
	$('#sign_in').validate({

			highlight: function (input) {
				$(input).parent('.form-group').addClass('has-error');
			},
			unhighlight: function (input) {
				$(input).parent('.form-group').removeClass('has-error');
			},
			errorPlacement: function (error, element) {
				//$(element).parents('.form-group').append(error);
			}
		});
	
	
	$('#sign_in').submit(function(e){
		e.preventDefault();
		if($('#sign_in').valid()) {
				$.ajax({
				type: "POST",
				url: "<?php echo base_url('student/login') ?>",
				data: $("#sign_in").serialize(),
				dataType: 'json',
				beforeSend: function (xhr) {
				  $("#sign_btn").prop("disabled",true);
				},
				success: function (res_data) {
					if (res_data[0].resSuccess == 1) { 
					  $("#sign_btn").prop("disabled",false);
					  window.location.href="<?php echo base_url('student/dashboard') ?>";
					}else if (res_data[0].resSuccess == 2){
						  $("#sign_btn").prop("disabled",false);
  						if(res_data[0].errtype == 'Validation'){
							creteToast('Login Error',res_data[0].arrError,'error');
							//$(".msg").html(res_data[0].arrError)
						}					
							return false;
						}					
					}
				});
			}
		return false;
		});
	
	});

	function creteToast(title,text,type){
		VanillaToasts.create({
			  // notification title
			  title: title,
			  // notification message
			  text: text,
			  // success, info, warning, error   / optional parameter
			  type: type, 
			  // path to notification icon
			  icon: '<?php echo base_url('assets/dist/img/logo-sm.png') ?>',
			  // topRight, topLeft, topCenter, bottomRight, bottomLeft, bottomCenter
			  positionClass: 'topCenter',
			  // auto dismiss after 5000ms
			  timeout: 5000 
			
			  // executed when toast is clicked
			  //callback: function() { ... } 
		
		});
		
	}



   </script>
</html>
