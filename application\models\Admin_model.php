<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
class Admin_model extends CI_Model {

    function __construct()
    {
        // Call the Model constructor
        parent::__construct();
		
    }
	
	public function validate_login($userid,$password){
		
		$this->db->select('id,name,email,password');
		$this->db->from('tbl_admin');
		
		$this->db->where(array('email' => $userid));
        $query = $this->db->get();
		
		if($query->num_rows() > 0 ){
			$row = $query->row();
			if($this->encryption->decrypt($row->password) == $password){
				$this->session->set_userdata('sess_user_id',$row->id);
				$this->session->set_userdata('sess_user_name',ucfirst($row->name));
				$this->session->set_userdata('sess_user_email',$row->email);
				$this->session->set_userdata('sess_user_domain','');
				$this->session->set_userdata('sess_admin_login',1);	
				return true;
			}else{
				return false;	
			}
		}else{
			return false;	
		}
	}
	
	public function validatemail($email){
		
		$this->db->select('id');
		$this->db->from('tbl_admin');
		$this->db->where('email', $email);
		$query = $this->db->get();
		if(!empty($query->row())){
			return true;
		}else{
			return false;
		}

	}
	
	
	public function getAdmindetails($email=''){
		$this->db->select('password');
		$this->db->from('tbl_admin');
		$this->db->where(array('id' => $this->session->userdata('sess_user_id')));
        $query = $this->db->get();
		return $query->row();		
	}
	
	
	public function checkRegNoinDB($regno){
		
		$this->db->select('regd_no');
		$this->db->from('tbl_students');
		$this->db->where('regd_no',$regno);
        $query = $this->db->get();
		if(!empty($query->row())){
			return true;	
		}else{
			return false;	
		}
	}
	
	
	public function addStudent($ins_data){
		$this->db->insert('tbl_students', $ins_data);
		$insert_id = $this->db->insert_id();
		return ($this->db->affected_rows() != 1) ? false : $insert_id;
		
	}
	
	public function insertbatchaddStudent($data){
		$this->db->insert_batch('tbl_students',$data);		
	}
	
	
	
	public function addResult($res_data){
		$this->db->insert('tbl_result', $res_data);
		$insert_id = $this->db->insert_id();
		return ($this->db->affected_rows() != 1) ? false : $insert_id;
	}
	
	
	public function addResultDetails($res_data){
		$this->db->insert('tbl_result_details', $res_data);
		$insert_id = $this->db->insert_id();
		return ($this->db->affected_rows() != 1) ? false : $insert_id;
		
	}
	
	
	public function insertBatchResultDetails($data){
		$this->db->insert_batch('tbl_result_details',$data);		
	}
	
	
	
	public function getStudent($regd_no){
		$this->db->select('regd_no,name,email,password,status');
		$this->db->from('tbl_students');
		$this->db->where('regd_no',$regd_no);
        $query = $this->db->get();
		return $query->row();		
		
	}
	
	public function updateStudent($regd_no, $data){ 
		$this->db->where('regd_no',$regd_no);
		$this->db->update('tbl_students', $data);
		//echo $this->db->last_query(); die();
		if($this->db->affected_rows() >=0){
			return true;	
		}else{
			return false;
		}
	}
	
	public function getStudentList($yr){
		$this->db->select('regd_no,name,email,password,status');
		$this->db->from('tbl_students');
		$this->db->where('SUBSTRING(regd_no,1,2)',$yr);
        $query = $this->db->get();
		return $query->result();		
		
	}
	
	
	public function getDistinctStudentYear(){
		
		$sql = "SELECT DISTINCT SUBSTRING(regd_no,1,2) as batch FROM `tbl_students` ORDER by batch";
		$query = $this->db->query($sql);
		return $query->result();
		
	}
	
	public function getStudentByResultId($id){
		$this->db->select('ts.regd_no,ts.name,ts.email,ts.status');
		$this->db->from('tbl_students ts');
		$this->db->join('tbl_result_details trd','trd.regd_no = ts.regd_no');
		$this->db->where(array('md5(trd.result_id)' => $id));
		$this->db->group_by('ts.regd_no'); 
		//$this->db->where("EXISTS(SELECT regd_no FROM tbl_result_details trd where md5(trd.result_id) = '" . $id."')");
	    $query = $this->db->get();
		//echo $this->db->last_query(); die();
		
		return $query->result();
				
	}
	
	
	
	
	public function updateProfile($data){ 
		$id = $this->session->userdata('sess_user_id');
		$this->db->where(array('id' => $id));
		$this->db->update('tbl_admin', $data);
		//echo $this->db->last_query(); die();
		if($this->db->affected_rows() >=0){
			return true;	
		}else{
			return false;
		}

		
	}
	
	
	public function resultset(){
		
		$this->db->select('tr.id,tr.title,tr.stream,tr.semester,tr.publish_date,tr.rechecking_allowed,tr.start_date,tr.end_date,tr.result_type,tr.status,tr.signature');
		$this->db->from('tbl_result tr');
		$this->db->order_by("tr.id", "DESC");
		$query = $this->db->get();
		//echo $this->db->last_query(); die('xxx');
		return $query->result();
			
	}
	
	public function exportResultSet($resultid){
				
		$this->db->select('trd.branch, trd.regd_no, ts.name, ts.email, trd.subject_code, trd.subject_name, trd.credits, trd.grade, trd.sgpa, trd.subject_type, tr.id, tr.title, tr.stream, tr.semester, tr.publish_date, tr.rechecking_allowed, tr.start_date, tr.end_date, tr.result_type, tr.status, ');
		$this->db->from('tbl_result as tr');		
		$this->db->join('tbl_result_details as trd','trd.result_id = tr.id');
		$this->db->join('tbl_students as ts','ts.regd_no = trd.regd_no');
		$this->db->order_by('trd.result_id ASC');
		$this->db->where(array('trd.result_id' => $resultid));
		$query = $this->db->get();
		//echo $this->db->last_query(); die('');
		return $query->result();
				
	}
		
	
	public function deleteFiles($path){
		$file = $path; 
		if(is_file($file)){
			unlink($file); 
		}
		return 1;   
	}
	
	public function getAdminUserDetails($adminemail){
		$this->db->order_by("ID", "ASC");
		$this->db->where(array('UserEmail' => $adminemail, 'Status' => 1));
        $query = $this->db->get("tbl_admin",1);
        return $query->row();
	}
	
	public function getAdminUserDetailsByEmail($adminemail){
		$this->db->select('UserName, UserEmail');
		$this->db->from('tbl_admin');
		$this->db->where(array('UserEmail' => $adminemail, 'Status' => 1));		
		$query = $this->db->get();		
		return $query->row();
	}
	
	
	public function getRegUsers(){
		$this->db->select('user_id, user_name, user_email, user_status');
		$this->db->from('tbl_users');
		$query = $this->db->get();		
		return $query->result();
		
	}
	
	public function getStatusById($userid){
		
		$this->db->select('user_status');
		$this->db->from('tbl_users');
		$this->db->where(array('user_id' => $userid));		
		$query = $this->db->get();		
		return $query->row();
		
	}
	
	public function updateStatusByID($userid,$status){
		$data = array(
			'user_status' => $status,
		);
		$this->db->where(array('user_id' => $userid));
		$this->db->update('tbl_users', $data);
		//echo $this->db->last_query(); die();
		if($this->db->affected_rows() >=0){
			return true;	
		}else{
			return false;
		}

	}
	
	
	
	public function deleteSet($id){
		
		$this->db->delete('tbl_result', array('id' => $id));
		return ($this->db->affected_rows() != 1) ? false : true;	
		
	}
	
	public function deleteResultDetails($id){
		$this->db->delete('tbl_result_details', array('result_id' => $id));
		return ($this->db->affected_rows() != 1) ? false : true;	
		
	}
	
	
	public function getResultSetById($id){
		
		$sql = 'SELECT id,title,stream,semester FROM tbl_result WHERE md5(id) = "' . $id . '"';
		//$this->db->select('tr.id,tr.title,tr.stream,tr.semester');
		//$this->db->from('tbl_result tr');
		//$this->db->where(array('md5(id)' => $id));
		//$this->db->where("EXISTS(SELECT regd_no FROM tbl_result_details trd where trd.result_id = tr.id)");
		$query = $this->db->query($sql);
		//echo $this->db->last_query(); die();
		return $query->row();
		
	}
	
	public function deleteStuResultSet($regd_no,$result_id){
		$this->db->delete('tbl_result_details', array('result_id' => $result_id,'regd_no' => $regd_no));
		return ($this->db->affected_rows() != 1) ? false : true;	
		
	}
	
	
	public function getStuResult($reg_no,$result_id){
		
		$this->db->select('tr.id,tr.title,tr.stream,tr.semester,tr.publish_date,tr.rechecking_allowed,tr.start_date,tr.end_date,tr.result_type,tr.signature,trd.branch, trd.subject_code, trd.subject_name, trd.credits, trd.grade, trd.sgpa, trd.subject_type,ts.regd_no, ts.name, ts.email');
		$this->db->from('tbl_result as tr');
		$this->db->join('tbl_result_details as trd','trd.result_id = tr.id');
		$this->db->join('tbl_students as ts','ts.regd_no = trd.regd_no');
		$this->db->where(array('trd.regd_no'=> $reg_no, 'md5(trd.result_id)' => $result_id));	
		$query = $this->db->get();
		//echo $this->db->last_query(); die();
		return $query->result();	
		
	}
	
	
	public function getResutByMd5OfresultId($res_id){
		$this->db->select('tr.id,tr.title,tr.stream,tr.semester,tr.publish_date,tr.rechecking_allowed,tr.start_date,tr.end_date,tr.result_type,tr.signature');
		$this->db->from('tbl_result as tr');
		$this->db->where(array('md5(tr.id)' => $res_id));	
		$query = $this->db->get();
		return $query->row();	
	}
	
	
	public function getResutByresultId($res_id){
		$this->db->select('tr.id,tr.title,tr.stream,tr.semester,tr.publish_date,tr.rechecking_allowed,tr.start_date,tr.end_date,tr.result_type,tr.signature');
		$this->db->from('tbl_result as tr');
		$this->db->where(array('tr.id' => $res_id));	
		$query = $this->db->get();
		return $query->row();	
	}
	
	
	public function getAllSturegNoAndSubjectCode($res_id){
		
		$this->db->select('regd_no,subject_code');
		$this->db->from('tbl_result_details');
		$this->db->where(array('result_id' => $res_id));
		$query = $this->db->get();
		return $query->result();
		
	}
	
	
	public function deleteResutDetailsByResId($res_id){
		$this->db->delete('tbl_result_details', array('result_id' => $res_id));
		return ($this->db->affected_rows() != 1) ? false : true;	

	}
	
	public function updateResult($data,$res_id){
		
		$this->db->where(array('id' => $res_id));
		$this->db->update('tbl_result', $data);
		//echo $this->db->last_query(); die();
		if($this->db->affected_rows() >=0){
			return true;	
		}else{
			return false;
		}
	}
	
	public function getAllRegNo(){
		
		$this->db->select('regd_no');
		$this->db->from('tbl_students');
		$query = $this->db->get();
		return $query->result();
	}
	
	public function detailRes($result_id){
		$this->db->select('regd_no,branch,subject_code,subject_name,credits,grade,sgpa,subject_type');
		$this->db->from('tbl_result_details');
		$this->db->where('result_id',$result_id);
		$query = $this->db->get();
		return $query->result();
	}
	
	
	
	public function getUserByEmail($email){
		$this->db->select('id,name,email');
		$this->db->from('tbl_admin');
		$this->db->where('email',$email);
		$query = $this->db->get();
		return $query->row();
		
	}
	
	
	public function getadmindetailsByMd5ID($id,$token){
		$this->db->select('id,name,email,token');
		$this->db->from('tbl_admin');
		$this->db->where('md5(id)',$id);
		$this->db->where('token',$token);
		$query = $this->db->get();
		return $query->row();
	}
	
	public function updateToken($data,$id){
		$this->db->where(array('id' => $id));
		$this->db->update('tbl_admin', $data);
		//echo $this->db->last_query(); die();
		if($this->db->affected_rows() >=0){
			return true;	
		}else{
			return false;
		}
		
		
	}
	
	
	public function updatePassword($data,$id){
		$this->db->where(array('id' => $id));
		$this->db->update('tbl_admin', $data);
		//echo $this->db->last_query(); die();
		if($this->db->affected_rows() >=0){
			return true;	
		}else{
			return false;
		}
		
	}
	
	
	public function validtaeIdToken($id,$token){
		
		$this->db->select('id');
		$this->db->from('tbl_admin');
		$this->db->where(array('id' => $id, 'token' => $token));
		$query = $this->db->get();
		if(!empty($query->row())){
			return true;	
		}else{
			return false;
		}
		
	}
	
	
	
	public function getstudentbyresid($res_id){
		$this->db->select('ts.regd_no,ts.name,ts.email,ts.password,ts.status');
		$this->db->from('tbl_students ts');
		$this->db->join('tbl_result_details trd', 'trd.regd_no = ts.regd_no');
		$this->db->where('trd.result_id',$res_id);
		$this->db->group_by('ts.regd_no');
		$query = $this->db->get();
		return $query->result();
		
	}
	
	/**********[Rechecking]****************************/
	
	
	public function recheckingResult(){
		$this->db->select('tr.id,tr.title,tr.stream,tr.semester,tr.publish_date,tr.rechecking_allowed,tr.start_date,tr.end_date,tr.result_type, tr.signature');
		$this->db->from('tbl_result tr');
		$this->db->where('tr.rechecking_allowed', '1');
		
		$query = $this->db->get();
		return $query->result();
		
	}
	
	
	public function getPaymentsByResId($res_id,$status){
		
		$this->db->select('tp.id,tp.regd_no,tp.result_id,tp.payment_date,tp.payment_amount,
						  tp.payment_status,tp.tnx_reference_no,tp.manually_approved,tp.manual_approved_date,tp.Remarks,ts.name,tp.txn_no,tp.error_code,tp.error_message,tp.created_at,tp.updated_at');	
		$this->db->from('tbl_payments tp');
		$this->db->join('tbl_students ts','ts.regd_no = tp.regd_no');
		$this->db->where('tp.result_id',$res_id);
		$this->db->where('tp.payment_status',$status);
		
		$query = $this->db->get();
		return $query->result();
		
	}
	
	
	
	
	public function updatePayments($pay_id,$data){
		
		$this->db->where(array('id' => $pay_id));
		$this->db->update('tbl_payments', $data);
		//echo $this->db->last_query(); die();
		if($this->db->affected_rows() >=0){
			return true;	
		}else{
			return false;
		}
		
		
	}
	
	
	
	public function paymentdetails($id){
		
		$this->db->select('tp.id,tp.regd_no,tp.result_id,tp.payment_date,
						  tp.payment_amount,tp.payment_status,tp.tnx_reference_no,tp.manually_approved,tr.title,tr.stream,tr.semester,
						  tp.manual_approved_date,tp.remarks,ts.name,ts.email');
		$this->db->from('tbl_payments tp');
		$this->db->join('tbl_result tr', 'tr.id = tp.result_id');
		$this->db->join('tbl_students ts', 'ts.regd_no = tp.regd_no');
		$this->db->where('tp.id',$id);
		$this->db->limit(1);		
		$query = $this->db->get();
		return $query->row();
		
	}
	
	
	public function getpaymentsubpayId($id){
		$this->db->select('subject_code,subject_name,rechecking_type');
		$this->db->from('tbl_payment_subjects');
		$this->db->where('payment_id',$id);
		
		$query = $this->db->get();
		return $query->result();
		
	}
	
	
	public function consolidatedResult($id){
		
		$this->db->select('tp.id,tp.regd_no,tp.result_id,tp.payment_date,
						  tp.payment_amount,tp.payment_status,tp.tnx_reference_no,tp.manually_approved,tr.title,tr.stream,tr.semester,
						  tp.manual_approved_date,tp.remarks,ts.name,ts.email,tps.subject_code,tps.subject_name,tps.rechecking_type');
		$this->db->from('tbl_payments tp');
		$this->db->join('tbl_result tr', 'tr.id = tp.result_id');
		$this->db->join('tbl_students ts', 'ts.regd_no = tp.regd_no');
		$this->db->join('tbl_payment_subjects tps', 'tps.payment_id = tp.id');
		$this->db->where('tp.result_id',$id);
		$this->db->where('tp.payment_status',1);
		$this->db->order_by('tp.regd_no,tps.subject_name');
		
		$query = $this->db->get();
		//echo $this->db->last_query(); die();
		return $query->result();
		
	}
	
	
	public function getPaidRes($requestData){
		
		$columns = array(
			  // datatable column index  => database column name
			  0 =>'tp.regd_no',
			  1 =>'ts.student_name',
			  2 => 'tp.payment_date'
			 
     
   		);
		
		$this->db->select('id');//s.photo_no,s.photo_name'
		$this->db->from('tbl_payments as tp');
		$this->db->where('tp.payment_status','1');
		$result = $this->db->get();
		$totalData = $result->num_rows();
		$totalFiltered = $totalData;  // when there is no search parameter then total number rows = total number filtered rows.		
		
		//$this->db->select('tp.id,tp.regd_no,tp.result_id,tp.payment_date,tp.payment_amount,
		///				  tp.payment_status,tp.tnx_reference_no,tp.manually_approved,tp.manual_approved_date,tp.Remarks,ts.name');	
		//$this->db->from('tbl_payments tp');
		//$this->db->join('tbl_students ts','ts.regd_no = tp.regd_no');
		//$this->db->where('tp.result_id',$res_id);

		$sql = "SELECT tp.id,tp.regd_no,tp.result_id,tp.payment_date,tp.payment_amount,
				tp.payment_status,tp.tnx_reference_no,tp.manually_approved,tp.manual_approved_date,tp.Remarks,ts.name";
   		$sql.=" FROM tbl_payments as tp JOIN tbl_students ts ON ts.regd_no = tp.regd_no AND where 1=1 ";
		// getting records as per search parameters
		$isFilterApply=0;
		if( !empty($requestData['search']['value']) ){   //name
		  $sql.=" AND ( tp.regd_no LIKE '".$requestData['search']['value']."%' ";
	 
			$sql.=" OR ts.name LIKE '".$requestData['search']['value']."%') ";
			$sql.=" OR tp.payment_date LIKE '".$requestData['search']['value']."%') ";
			$isFilterApply=1;
		  }
	 
	 
		  $sql.=" ORDER BY ". $columns[$requestData['order'][0]['column']]."   ".$requestData['order'][0]['dir']."   LIMIT ".$requestData['start']." ,".$requestData['length']."   ";  // adding length
		  $result1 = $this->db->query($sql);
		  
		  if($isFilterApply==1){
			$totalFiltered =  $result1->num_rows(); 
		  }
	 
		   // when there is a search parameter then we have to modify total number filtered rows as per search result.
		  $row=$result1->result_array();
		  for ($i=0; $i < count($row); $i++) {
			$row[$i]['action']='';
		  }
		  $json_data = array(
			"draw"            => intval( $requestData['draw'] ),   // for every request/draw by clientside , they send a number as a parameter, when they recieve a response/data they first check the draw number, so we are sending same number in draw.
			"recordsTotal"    => intval( $totalData ),  // total number of records
			"recordsFiltered" => intval( $totalFiltered ), // total number of records after searching, if there is no searching then totalFiltered = totalData
			"data"            =>   $row  // total data array
	 
		  );
		  return $json_data;		
		
	}
	
	
	/*-----------------------Dashboard------------------------*/
	
	
	public function getTotalResultSet(){
		
		$this->db->select('count(id) as total_res_set');
		$this->db->from('tbl_result');
		$query = $this->db->get();
		if(!empty($query->row())){
			return $query->row()->total_res_set;
		}else{
			return 0;
		}
	}
	
	
	public function getTotalStudent(){
		
		$this->db->select('count(regd_no) as total_stu');
		$this->db->from('tbl_students');
		$query = $this->db->get();
		if(!empty($query->row())){
			return $query->row()->total_stu;
		}else{
			return 0;
		}
		
	}
	
	
	
	public function getAllowedRechecking(){
		$this->db->select('count(id) as allowed_recheck');
		$this->db->from('tbl_result');
		$this->db->where('rechecking_allowed','1');
		$query = $this->db->get();
		if(!empty($query->row())){
			return $query->row()->allowed_recheck;
		}else{
			return 0;
		}
	
	}
	
	
	public function getPaidPayment(){
		
		$this->db->select('count(id) as total_paid');
		$this->db->from('tbl_payments');
		$this->db->where('payment_status','1');
		$query = $this->db->get();
		if(!empty($query->row())){
			return $query->row()->total_paid;
		}else{
			return 0;
		}

	}
	
	
	
	public function getResultsetStatus($res_id){
		$this->db->select('status');
		$this->db->from('tbl_result');
		$this->db->where('id',$res_id);
		$query = $this->db->get();
		return $query->row();
		
	}
	
	
	public function updateResultsetStstus($data,$res_id){
		
		$this->db->where(array('id' => $res_id));
		$this->db->update('tbl_result', $data);
		if($this->db->affected_rows() >=0){
			return true;	
		}else{
			return false;
		}
		
	}
	
	public function ExportPaymentsByResId($res_id){
		
		$this->db->select('tp.id,tp.regd_no,tp.result_id,tp.payment_date,tp.payment_amount,
						  tp.payment_status,tp.tnx_reference_no,tp.manually_approved,tp.manual_approved_date,tp.Remarks,ts.name');	
		$this->db->from('tbl_payments tp');
		$this->db->join('tbl_students ts','ts.regd_no = tp.regd_no');
		$this->db->where('tp.payment_status',1);
		$this->db->where('tp.result_id',$res_id);		
		$this->db->order_by('tp.id','ASC');		
		$query = $this->db->get();
		return $query->result();		
	}
	
	public function exportPaymentsFailedByResId($res_id){
		
		$this->db->select('tp.id,tp.regd_no,tp.result_id,tp.payment_date,tp.payment_amount,
						  tp.payment_status,tp.tnx_reference_no,tp.manually_approved,tp.manual_approved_date,tp.Remarks,ts.name');	
		$this->db->from('tbl_payments tp');
		$this->db->join('tbl_students ts','ts.regd_no = tp.regd_no');
		#$this->db->where('tp.payment_status',0);
		$this->db->where_in('tp.payment_status', [0, 2]);
		$this->db->where('tp.result_id',$res_id);		
		$this->db->order_by('tp.id','ASC');		
		$query = $this->db->get();
		return $query->result();
		
	}
	
	public function exportPaymentSubjectsByResId($res_id){
		
		$this->db->select('tp.id,tp.regd_no,tp.result_id,DATE_FORMAT(tp.payment_date, "%d-%m-%Y") AS payment_date, ,tp.payment_amount,tp.payment_status,tps.subject_code,tps.subject_name,tps.rechecking_type,ts.name');	
		$this->db->from('tbl_payments tp');
		$this->db->join('tbl_payment_subjects tps','tps.payment_id = tp.id');
		$this->db->join('tbl_students ts','ts.regd_no = tp.regd_no');		
		$this->db->where(array('tp.payment_status' => 1, 'tp.result_id' => $res_id));
		$this->db->order_by('tp.id','ASC');		
		$query = $this->db->get();		
		//echo $this->db->last_query(); exit('');
		return $query->result();		
	}
	
}