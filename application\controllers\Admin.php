<?php
defined('BASEPATH') OR exit('No direct script access allowed');
ini_set('memory_limit', '-1');
require 'vendor/autoload.php';
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class Admin extends CI_Controller {
	
	function __construct() {
        parent::__construct();
		
		$this->load->helper('text');
		$this->load->library(array('encryption', 'form_validation'));
		$this->load->model('admin_model', 'adminModel');
		
		
		/*echo $this->encryption->decrypt('c8702475520a4dce5e78cfff81c3778f6fcb2cfffa781ee2f38806c3727e130cbe50fc97b21156060f06fd3b865d34d0c7cb8ec0d501c2febf858086b641f861RIOuKIOkcucaxGd5T4Wzt8OnYrtRJRy/BhY8Jpyk0j0='); die();*/
		
		/*echo $this->encryption->decrypt('8a622cb92cffe85803a63f27db0386e1a162c9aec47703e7f80c40120e6253e80bab3c014db13ab1ac26861b6a1ec967d85564a97e35095da21b1d501cc5b0cao6+OQ/Yn9yHMh32tQ5pf5X8hy9qw/atb/qqsUxEaMaw='); die();*/

	}
		
	public function index(){ 
		$this->load->view('_admin/login');
	}
	
	public function login(){
		$result = array();
		$this->form_validation->set_rules('userid', "userid", 'trim|required|callback__validate_login');
		if ($this->form_validation->run() == FALSE){
			$result[] = array('resSuccess' => 2, 'msg' => 'Error', 'errtype' => 'Validation', 'arrError' => validation_errors());
			echo json_encode($result);			
			exit();
		}else{
			$result[] = array('resSuccess' => 1, 'msg' => 'Success');
			echo json_encode($result);
			exit();				
		}

		
	}
	
	public function _validate_login(){
		
		$userid = trim($this->input->post("userid"));
		$pwd = trim($this->input->post("password"));

		if($userid!="" && $pwd!=""){
			if($this->adminModel->validate_login($userid,$pwd)){
				return true;	
			}else{
				$this->form_validation->set_message('_validate_login', 'Unauthenticated User! Access Denied.');
          		return false;
			}
		}
	}
	
	public function forgotpwd(){
		$data = array();
		$data['menu'] = '';
		//$this->load->view('_admin_shared/header',$data);	
		$this->load->view('_admin/forgot',$data);
		//$this->load->view('_admin_shared/footer',$data);
		
	}
	
	
	public function sendpwd(){

		$data = array();
		$data['menu'] = '';
		$email = $this->input->post('email',TRUE);
		
		
		$admin_dt = $this->adminModel->getUserByEmail($email);
		if(!empty($admin_dt)){
			
			$id = $admin_dt->id;
			$str_result = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'; 
			$length_of_string = 5;
			$token = substr(str_shuffle($str_result),  0, $length_of_string);
			$token = md5($token); 
			$data['id'] = md5($admin_dt->id);
			$data['token'] = $token;
			
			$data_token = array('token' => $token);	
			
			$this->adminModel->updateToken($data_token,$id);
			 
			
			$msg = $this->load->view('_admin/email',$data,TRUE);
			
			$from_email =  $admin_dt->email;
			//$to_email = $admin_dt->email;
			$to_email = "<EMAIL>";
			
			$config = array(
                'protocol' => 'smtp',
                'smtp_host' => 'ssl://smtp.googlemail.com',
                'smtp_port' => 465,
                'smtp_user' => '<EMAIL>',
                'smtp_pass' => 'Result#2021',
                'mailtype'  => 'html', 
                'charset'   => 'iso-8859-1'
            );
            $this->load->library('email', $config);
            $this->email->set_newline("\r\n");	
					
			$this->email->from($from_email, 'CET - RESULT'); 
			$this->email->to($to_email);
			$this->email->subject('Password Reset'); 
			$this->email->message($msg); 
	
			//Send mail 
			if (!$this->email->send()) {
				//echo $this->email->print_debugger()
				//$result[] = array('resSuccess' => 2, 'msg' => 'Unable To send msg.');
				$result[] = array('resSuccess' => 2, 'msg' => $this->email->print_debugger());
				echo json_encode($result);
				exit();	
			}else{
				$result[] = array('resSuccess' => 1, 'msg' => 'Success');
				echo json_encode($result);
				exit();	
			}
			
		}else{
			$result[] = array('resSuccess' => 2, 'msg' => $email . ' is not registred.');
			echo json_encode($result);			
			exit();
			
		}
		
	}
	
	
	
	/*public function sendmail(){
		
		$this->load->view('_admin/email');
		
	}*/
	
	
	
	
	
	
	public function resetpwd($id,$token){
		
		//echo $id; die('www');
		$data['menu'] = '';
		$data['admin_res'] = $this->adminModel->getadmindetailsByMd5ID($id,$token);
		
		if(!empty($data['admin_res'])){
			$this->load->view('_admin/reset',$data);
		}else{
			$this->load->view('_admin/error');	
			
		}
	
	
	}
	
	
	
	
	public function updatepassword(){
		
		$recid = $this->input->post('recid',TRUE);
		$task = $this->input->post('task',TRUE);
		$token = $this->input->post('token',TRUE);
		$new_pwd = $this->input->post('newpassword',TRUE);
		
		
		if($this->adminModel->validtaeIdToken($recid,$token)){
			
			$this->db->trans_strict(TRUE);	
			$this->db->trans_begin();

			
			$data_pw = array('password' => $this->encryption->encrypt($new_pwd),
							 'token' => NULL);
			
			
			$this->adminModel->updatePassword($data_pw,$recid);
			
			
			if($this->db->trans_status() === TRUE){
				$this->db->trans_commit(); 
				$result[] = array('resSuccess' => 1, 'msg' => 'Password changed successfully');
				echo json_encode($result);
				exit();				
				
			}else{
				 $this->db->trans_rollback(); 
				$result[] = array('resSuccess' => 2, 'msg' => 'Unable to change password');
				echo json_encode($result);			
				exit();
			}	
			
			
			
			
			
			
			
		}else{
			$result[] = array('resSuccess' => 2, 'msg' => 'Invalid Request');
			echo json_encode($result);			
			exit();
		}
		
	}
	
	
	public function logout(){
		
		$this->session->sess_destroy();
		redirect('admin/');

	}
	
	public function dashboard(){
		
		$this->load->library('Admin_acess');
		$data["menu"] = "dashboard";
		
		
		$data['total_result_set'] = $this->adminModel->getTotalResultSet();
		$data['total_student'] = $this->adminModel->getTotalStudent();
		$data['allowed_rechecking'] = $this->adminModel->getAllowedRechecking();
		$data['successfull_payment'] = $this->adminModel->getPaidPayment();
		
		
		$this->load->view('_admin_shared/header',$data);	
		$this->load->view('_admin/dashboard',$data);
		$this->load->view('_admin_shared/footer',$data);
	}
	
	
	public function changepwd(){
		$this->load->library('Admin_acess');
		$data["menu"] = "";
		$this->load->view('_admin_shared/header',$data);	
		$this->load->view('_admin/changepwd',$data);
		$this->load->view('_admin_shared/footer',$data);
		
	}
	
	
	
	public function saveprofile(){
		$this->load->library('Admin_acess');
		$data["menu"] = "";

		$name = $this->input->post('name',TRUE);
		$email = $this->input->post('email',TRUE);
		$old_password = $this->input->post('old_password',TRUE);
		$new_password = $this->input->post('new_password',TRUE);
		
		
		$this->db->trans_strict(TRUE);	
		$this->db->trans_begin();

			$profile_info = $this->adminModel->getAdmindetails();
			if(!empty($profile_info)){
				
				if($this->encryption->decrypt($profile_info->password) != $old_password){
					$result[] = array('resSuccess' => 2, 'msg' => 'Incorrect old password provided');
					echo json_encode($result);			
					exit();
				}else{
					
					if(!empty($new_password)){
						
						$data_pwd = array('password' => $this->encryption->encrypt($new_password));
						$this->adminModel->updateProfile($data_pwd);
					}
				}
				
			}else{
					$result[] = array('resSuccess' => 2, 'msg' => 'Profile not found.');
					echo json_encode($result);			
					exit();
			}
			
		
		if($this->db->trans_status() === TRUE){
			$this->db->trans_commit(); 
			$result[] = array('resSuccess' => 1, 'msg' => 'Profile saved successfully');
			echo json_encode($result);
			exit();				
			
		}else{
			 $this->db->trans_rollback(); 
			$result[] = array('resSuccess' => 2, 'msg' => 'Unable to save profile');
			echo json_encode($result);			
			exit();
		}	
		
			
		
	}
	
	
	
	public function importresult($res_id = ''){
		$this->load->library('Admin_acess');
		$data["menu"] = "importresult";
		$data['streams'] = $this->config->item('streams');
		$data['res_id'] = $res_id;
		$data['signatures'] = $this->config->item('coe_signature');
		
		if(!empty($res_id)){
			$data['results'] = $this->adminModel->getResutByresultId($res_id);
		}
		
		$this->load->view('_admin_shared/header',$data);	
		$this->load->view('_admin/import_result',$data);
		$this->load->view('_admin_shared/footer',$data);
		
	}
	
	
	
	
	public function studentinfo(){
		$this->load->library('Admin_acess');
		$data["menu"] = "studentinfo";
		
		$file_name = 'assets/student_result_excel/result_data_v2.xlsx';
		$file_type = \PhpOffice\PhpSpreadsheet\IOFactory::identify($file_name);
 		$reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($file_type);
		
 		$spreadsheet = $reader->load($file_name);
		$data['stu_data'] = $spreadsheet->getActiveSheet()->toArray();
		$data['success_record'] = array();
		$data['failed_rescord'] = array();
		$dat_dup_array = array();

		//print_r($stu_data); die();
		$i=1;
		foreach($data['stu_data'] as $stu){
			if($i == 1){
				$i++;
				continue;	
			}
			
			if(array_key_exists($stu[1],$dat_dup_array) && $dat_dup_array[$stu[1]] == $stu[4]){
				$data['failed_rescord'] = $stu;
				
			}else{
				$dat_dup_array[$stu[1]] = $stu[4];
				$data['success_record'][] = $stu;
			}
			
			
			
			
		}
		
		
		
	//	print_r($data['success_record']);
	//	die();
		
		
		
		

		$this->load->view('_admin_shared/header',$data);	
		$this->load->view('_admin/stu_info',$data);
		$this->load->view('_admin_shared/footer',$data);

		

		
		
		
	}
	
	
	public function savetempresult(){
		
// 		print_r($_POST);
// 		print_r($_FILES);
// 		die();
		$this->load->library('Admin_acess');
		$data["menu"] = "importresult";
		$data["resinfo"] = $_POST;
		
		if(empty($data["resinfo"])){
			redirect('admin/importresult');	
		}
		
		
		
		
		
			$target_dir = "assets/student_result_excel/";
			$result_title = $this->input->post('result_title',TRUE);
			$stream = $this->input->post('stream',TRUE);
			$semester = $this->input->post('semester',TRUE);
			$publish_date = $this->input->post('publish_date',TRUE);
			$result_type = $this->input->post('result_type',TRUE);
			$rechecking_allowed = $this->input->post('rechecking_allowed',TRUE);
			$signatures = $this->input->post('signatures',TRUE);
			$task = $this->input->post('task',TRUE);
			$recid = $this->input->post('recid',TRUE);
			$auto_delete =  $this->input->post('hdn_auto_delete',TRUE);
			$import_only =  $this->input->post('hdn_import_only',TRUE);
			
			if(!empty($_POST['start_date'])){
				$start_date = $this->input->post('start_date',TRUE);
			}
			if(!empty($_POST['end_date'])){
				$end_date = $this->input->post('end_date',TRUE);
			}
			
			
			if(!empty($_FILES['importresult']['name'])){
				
				$newfilename= date('dmYHis').'_'.str_replace(" ", "", basename($_FILES["importresult"]["name"]));
				
				
				if(move_uploaded_file($_FILES["importresult"]["tmp_name"], $target_dir .$newfilename)){
					
					//echo 'success';
					//exit;
					
				}
				
			}
			
		$data['filename'] = $newfilename;
		$file_name = 'assets/student_result_excel/' . $newfilename;
		$file_type = \PhpOffice\PhpSpreadsheet\IOFactory::identify($file_name);
 		$reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($file_type);
		
 		$spreadsheet = $reader->load($file_name);
		$data['stu_data'] = $spreadsheet->getActiveSheet()->toArray();
		$data['success_record'] = array();
		$data['failed_record'] = array();
		$exist_in_db = array();
		$dat_dup_array = array();
		$data['total_record'] = 0;
		$data['streams'] = $this->config->item('streams');
		
		//die($import_only . ' -- ' . $recid);
		//check ony the new recorde will be shown
		//echo $import_only; die();
		if($import_only){
			$res_info = $this->adminModel->getAllSturegNoAndSubjectCode($recid);
			
			foreach($res_info as $val){
				$exist_in_db[$val->regd_no][] = $val->subject_code;
				$dat_dup_array[$val->regd_no][] = $val->subject_code;
				
			}
		}
		

		//print_r($dat_dup_array); die();
		$i=1;
		foreach($data['stu_data'] as $stu){
			
		if(!empty($stu[1])){	
					if($i == 1){
						$i++;
						continue;	
					}
					
					$data['total_record']++;
					
					if($task == 1){
					
						if(array_key_exists($stu[1],$dat_dup_array) && in_array($stu[4], $dat_dup_array[$stu[1]])){
							$data['failed_record'][] = array_merge($stu,array('Duplicate entry In excel'));
							//exit('dddd');
						}else{
							
							if(empty($stu[1])){
								$data['failed_record'][] = array_merge($stu,array('Regdno required In excel'));							
							}else if(empty($stu[2])){
								$data['failed_record'][] = array_merge($stu,array('Name required In excel'));
							}else if(empty($stu[4])){
								$data['failed_record'][] = array_merge($stu,array('Subcode required In excel'));
							}else if(empty($stu[5])){
								$data['failed_record'][] = array_merge($stu,array('Subject required In excel'));
							}
							/*else if(empty($stu[6])){
								$data['failed_record'][] = array_merge($stu,array('Credits required In excel'));
							}*//*else if($stu[9]!='T' || $stu[9]!='P'){
								$data['failed_record'][] = array_merge($stu,array('Type must be T or P In excel'));
								
							}*/else{
							
								$dat_dup_array[$stu[1]][] = $stu[4];
								$data['success_record'][] = $stu;
							}
						}
					}else{ 
						
						//$data['total_record']++;
						//Delete and reinsert all
						//if($auto_delete){
							
							
							//echo 
						
						if($auto_delete){ 
							
							
							if(array_key_exists($stu[1],$dat_dup_array) && in_array($stu[4], $dat_dup_array[$stu[1]])){		
									$data['failed_record'][] = array_merge($stu,array('Duplicate entry In excel'));
							}else{
								
									if(empty($stu[1])){
										$data['failed_record'][] = array_merge($stu,array('Regdno required In excel'));
									
									}else if(empty($stu[2])){
										$data['failed_record'][] = array_merge($stu,array('Name required In excel'));
									
									}else if(empty($stu[4])){
										$data['failed_record'][] = array_merge($stu,array('Subcode required In excel'));
									
									}else if(empty($stu[5])){
										$data['failed_record'][] = array_merge($stu,array('Subject required In excel'));
									
									}/*else if(empty($stu[6])){
										$data['failed_record'][] = array_merge($stu,array('Credits required In excel'));
										
									}*//*else if($stu[9]!='T' || $stu[9]!='P'){
										$data['failed_record'][] = array_merge($stu,array('Type must be T or P In excel'));
										
									}*/else{
		
										$dat_dup_array[$stu[1]][] = $stu[4];
										$data['success_record'][] = $stu;
									}
							}
		
							
						}else{
						
							
							if(array_key_exists($stu[1],$dat_dup_array) &&  in_array($stu[4], $dat_dup_array[$stu[1]])){
								
								if(array_key_exists($stu[1],$exist_in_db) &&  in_array($stu[4], $exist_in_db[$stu[1]])){
									$data['failed_record'][] = array_merge($stu,array('Exist in database'));
								}else{
									$data['failed_record'][] = array_merge($stu,array('Duplicate entry In excel'));
								}
								
							}else{
								
										if(empty($stu[1])){
											$data['failed_record'][] = array_merge($stu,array('Regdno required In excel'));
										
										}else if(empty($stu[2])){
											$data['failed_record'][] = array_merge($stu,array('Name required In excel'));
										
										}else if(empty($stu[4])){
											$data['failed_record'][] = array_merge($stu,array('Subcode required In excel'));
										
										}else if(empty($stu[5])){
											$data['failed_record'][] = array_merge($stu,array('Subject required In excel'));
										
										}/*else if(empty($stu[6])){
											$data['failed_record'][] = array_merge($stu,array('Credits required In excel'));
											
										}*//*else if($stu[9]!='T' || $stu[9]!='P'){
											$data['failed_record'][] = array_merge($stu,array('Type must be T or P In excel'));
											
										}*/else{
									
											$dat_dup_array[$stu[1]][] = $stu[4];
											$data['success_record'][] = $stu;
									   }
							}
							
							
						}
							
							
							//print_r($dat_dup_array); die();
							
					
						//}
						
						
					}
					
				}
		}
		
		
		
		//print_r($data['success_record']);
		//print_r($data['failed_record']);
		//die();
		/*$table ='';
		$table ='<table><tr><td>Branch</td><td>regdno</td><td>name</td><td>email</td><td>subcode</td><td>subject</td><td>credits</td><td>grade</td><td>sgpa</td><td>type</td></tr>';
		
		foreach($data['success_record'] as $success){
				
				$table.= '<tr><td>' .  $success[0] . '</td><td>' .$success[1] . '</td><td>' . $success[2] . '</td><td>' . $success[3] . '</td><td>' . $success[4] . '</td><td>' . $success[5] . '</td><td>' . $success[6] . '</td><td>' . $success[7] . '</td><td>' . $success[8] . '</td><td>' . $success[9]  .'</td></tr>';	
			
		}
		$table.='</table>';
		echo $table;
		die('--');*/
		
		
		/* --------------------Generate successfull excel and use it on saving ----------------------*/
		
		
		$spreadsheet_new = new Spreadsheet(); // instantiate Spreadsheet
		$sheet = $spreadsheet_new->getActiveSheet();
		
		$table_columns = array("branch",
							   "regdno",
							   "name",
							   "email",
							   "subcode",
							   "subject",
							   "credits",
							   "grade",
							   "sgpa",
							   "type");
		
		//Generate The Headers Here
		$sheet->fromArray($table_columns, NULL, 'A1');
		
		$row_no = 2;
		foreach($data['success_record'] as $success){
			//echo $value->CompanyName; die('hello');
			$sheet->setCellValue('A' . $row_no, trim($success[0]));
			$sheet->setCellValue('B' . $row_no, trim($success[1]));
			$sheet->setCellValue('C' . $row_no, trim($success[2]));
			$sheet->setCellValue('D' . $row_no, trim($success[3]));
			$sheet->setCellValue('E' . $row_no, trim($success[4]));
			$sheet->setCellValue('F' . $row_no, trim($success[5]));
			$sheet->setCellValue('G' . $row_no, trim($success[6]));
			$sheet->setCellValue('H' . $row_no, trim($success[7]));
			$sheet->setCellValue('I' . $row_no, trim($success[8]));
			$sheet->setCellValue('J' . $row_no, trim($success[9]));

			$row_no++;
		}
		
		
		//Will set the dimensioon to text width
		foreach (range('A','J') as $col) {
 			 $sheet->getColumnDimension($col)->setAutoSize(true);  
		}
		
		$writer = new Xlsx($spreadsheet_new);
		
		if(file_exists($file_name)){
			@unlink($file_name);
		}
		
		$writer->save($file_name); //Save file 
		
		/*---------------------------End Of excel------------------------------------------------------*/
		

		$this->load->view('_admin_shared/header',$data);	
		$this->load->view('_admin/stu_info',$data);
		$this->load->view('_admin_shared/footer',$data);
		
	}
	
	
	
	
	public function saveresultset(){
					
		$this->load->library('Admin_acess');
		$data["menu"] = "importresult";
		$streams = $this->config->item('streams');
		$title = $this->input->post('result_title',TRUE);
		$stream = $this->input->post('stream',TRUE);
		$stream = $streams[$stream][0];
		$semester = $this->input->post('semester',TRUE);
		$publish_date = $this->input->post('publish_date',TRUE);
		$result_type = $this->input->post('result_type',TRUE);
		$rechecking_allowed = $this->input->post('rechecking_allowed',TRUE);
		$signature = $this->input->post('signature',TRUE);
		$filename = $this->input->post('filename',TRUE);
		
		$task = $this->input->post('task',TRUE);
		$recid = $this->input->post('recid',TRUE);
		$auto_delete = !(empty($_POST['hdn_auto_delete'])?1:0);
		$import_only = !(empty($_POST['hdn_import_only'])?1:0);

		
		$start_date = '';
		$end_date = '';
		if($rechecking_allowed){
			if(!empty($_POST['start_date'])){
				$start_date = date('Y-m-d',strtotime($_POST['start_date']));
			}
			if(!empty($_POST['end_date'])){
				$end_date = date('Y-m-d',strtotime($_POST['end_date']));
			}
			
		}
		
		
		$task = $this->input->post('task',TRUE);
		$recid = $this->input->post('recid',TRUE);
		
		$exist_in_db = array();
		$dat_dup_array = array();
		$total_reg_no = array();
		
		$total_stu = $this->adminModel->getAllRegNo();
		foreach($total_stu as $streg){
			$total_reg_no[] = $streg->regd_no;
		}
		
		
		if($import_only){
			$res_info = $this->adminModel->getAllSturegNoAndSubjectCode($recid);
			
			foreach($res_info as $val){
				$exist_in_db[$val->regd_no][] = $val->subject_code;
				$dat_dup_array[$val->regd_no][] = $val->subject_code;
				
			}
		}
		
		
		
		
		
		
		$file_name = 'assets/student_result_excel/'. $filename;
		$file_type = \PhpOffice\PhpSpreadsheet\IOFactory::identify($file_name);
 		$reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($file_type);

 		$spreadsheet = $reader->load($file_name);
		
		$stu_data = $spreadsheet->getActiveSheet()->toArray();
		
		
		$str_result = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'; 
		$length_of_string = 8;
		$pwd = '';
		$i=1;
		
		$this->db->trans_strict(TRUE);	
		$this->db->trans_begin();
		
		if($task == 1){

						$res_dt = array('title' => addslashes($title),
								'stream' => $stream,
								'semester' => $semester,
								'publish_date' => date('Y-m-d' ,strtotime($publish_date)),
								'rechecking_allowed' => $rechecking_allowed,
								'start_date' => !empty($start_date)?$start_date:NULL,
								'end_date' => !empty($end_date)?$end_date:NULL,
								'result_type' => $result_type,
								'signature' => $signature
								);
						
						
						$result_id = $this->adminModel->addResult($res_dt);		
				
						
						$res_st_count = 1;
						$res_dt_count = 1;
						$ins_stu = array();
						$result_dt_details = array();
						
						
						foreach($stu_data as $dt){
							if(!empty($dt[1])){	
								if($i == 1){
									$i++;
									continue;	
								}
								$pwd = substr(str_shuffle($str_result),  0, $length_of_string);
							
								if(!in_array($dt[1] , $total_reg_no)){	
											
									if($res_st_count <=100){
										
										$ins_stu[] = array('regd_no' => $dt[1] ,
														 'name' => $dt[2],
														 'email' => $dt[3],
														 'password' => $this->encryption->encrypt($pwd),
														 'status' => '1');	
										$res_st_count++;				 
										
									}
									
									
									if(count($ins_stu)  == 100){
										
										$this->adminModel->insertbatchaddStudent($ins_stu);
										$ins_stu = array();
										$res_st_count = 1;
									}
											
									array_push($total_reg_no,$dt[1]);
								
								}
										
										
								if($res_dt_count <=100){
									
									$result_dt_details[] = 	array('result_id' => $result_id,
																  'regd_no' => $dt[1],
																  'branch' =>  $dt[0],
																  'subject_code' => $dt[4] ,
																  'subject_name' => $dt[5] ,
																  'credits' => $dt[6],
																  'grade' => $dt[7], 
																  'sgpa' => $dt[8],
																  'subject_type' => $dt[9]);
								
									$res_dt_count++;	
								}
						
						
								
								
						
								if(count($result_dt_details)  == 100){
									
									$this->adminModel->insertBatchResultDetails($result_dt_details);
									$result_dt_details = array();
									$res_dt_count = 1;
								}
								
								
							}
					
						}
						
						if(!empty($ins_stu)){
							$this->adminModel->insertbatchaddStudent($ins_stu);
						}
						if(!empty($result_dt_details)){
							$this->adminModel->insertBatchResultDetails($result_dt_details);
						}
										
					
						
		}else{
			
			$res_dt = array('title' => addslashes($title),
							'stream' => $stream,
							'semester' => $semester,
							'publish_date' => date('y-m-d' ,strtotime($publish_date)),
							'rechecking_allowed' => $rechecking_allowed,
							'start_date' => !empty($start_date)?$start_date:NULL,
							'end_date' => !empty($end_date)?$end_date:NULL,
							'result_type' => $result_type,
							'signature' => $signature);
						
						
			$this->adminModel->updateResult($res_dt,$recid);
			$result_id = $recid;
			if($auto_delete){	
				$this->adminModel->deleteResutDetailsByResId($recid);
			}
			

			$res_st_count = 1;
			$res_dt_count = 1;
			$ins_stu = array();
			$result_dt_details = array();

			foreach($stu_data as $dt){
				if(!empty($dt[1])){	
					if($i == 1){
						$i++;
						continue;	
					}
					$pwd = substr(str_shuffle($str_result),  0, $length_of_string);
					
					//Insert student info
					if(!in_array($dt[1] , $total_reg_no)){	
								
						if($res_st_count <=100){
							$ins_stu[] = array('regd_no' => $dt[1] ,
											 'name' => $dt[2],
											 'email' => $dt[3],
											 'password' => $this->encryption->encrypt($pwd),
											 'status' => '1');	
							$res_st_count++;				 
						}
								
								
						if(count($ins_stu)  == 100){
							$this->adminModel->insertbatchaddStudent($ins_stu);
							$ins_stu = array();
							$res_st_count = 1;
						}
						
						array_push($total_reg_no,$dt[1]);
					}									
							
					// Auto Delete
					if($auto_delete){	
						
						if($res_dt_count <=100){
							$result_dt_details[] = 	array('result_id' => $result_id,
														  'regd_no' => $dt[1],
														  'branch' =>  $dt[0],
														  'subject_code' => $dt[4] ,
														  'subject_name' => $dt[5] ,
														  'credits' => $dt[6],
														  'grade' => $dt[7], 
														  'sgpa' => $dt[8],
														  'subject_type' => $dt[9]);
						
							$res_dt_count++;	
						}
							
						if(count($result_dt_details)  == 100){
							$this->adminModel->insertBatchResultDetails($result_dt_details);
							$result_dt_details = array();
							$res_dt_count = 1;
						}

					}// end of auto delete
						
						
						
					if($import_only){
							
						if(array_key_exists($dt[1],$dat_dup_array) &&  in_array($dt[4], $dat_dup_array[$dt[1]])){
							//do Nothing
						}else{
								
							if($res_dt_count <=100){
								
								$result_dt_details[] = 	array('result_id' => $result_id,
															  'regd_no' => $dt[1],
															  'branch' =>  $dt[0],
															  'subject_code' => $dt[4] ,
															  'subject_name' => $dt[5] ,
															  'credits' => $dt[6],
															  'grade' => $dt[7], 
															  'sgpa' => $dt[8],
															  'subject_type' => $dt[9]);
							
								$res_dt_count++;	
							}
							
							if(count($result_dt_details)  == 100){
								
								$this->adminModel->insertBatchResultDetails($result_dt_details);
								$result_dt_details = array();
								$res_dt_count = 1;
							}
							
						}
							
					} //End of import only
			
				}		

			} //end foreach
			
									
			if(!empty($ins_stu)){
				$this->adminModel->insertbatchaddStudent($ins_stu);
				$res_st_count = 1;
			}
			if(!empty($result_dt_details)){
				$this->adminModel->insertBatchResultDetails($result_dt_details);
				$res_dt_count = 1;
			}									

			
		}
		
		
		
		
		if($this->db->trans_status() === TRUE){
			$this->db->trans_commit(); 
			if(file_exists($file_name)){
				@unlink($file_name);	
			}
			
			$result[] = array('resSuccess' => 1, 'msg' => 'Result saved successfully');
			echo json_encode($result);
			exit();				
			
		}else{
			 $this->db->trans_rollback(); 
			$result[] = array('resSuccess' => 2, 'msg' => 'Unable to save Result');
			echo json_encode($result);			
			exit();
		}	
		
	}
	
	
	public function resultset(){
		$this->load->library('Admin_acess');
		$data["menu"] = "viewresult";
		$data["results"] = $this->adminModel->resultset();
		//print_r($data["results"]); die();
		
		$this->load->view('_admin_shared/header',$data);	
		$this->load->view('_admin/result_set_list',$data);
		$this->load->view('_admin_shared/footer',$data);
		
	}
	
	public function disable($id){
		$this->load->library('Admin_acess');
		$res = $this->adminModel->getResultsetStatus($id);
		$data = array();
		$this->db->trans_strict(TRUE);	
		$this->db->trans_begin();

			if($res->status == '1'){
				$data = array('status' => '0');
			}else{
				$data = array('status' => '1');
			}
			
			$this->adminModel->updateResultsetStstus($data,$id);
	
		if($this->db->trans_status() === TRUE){
			$this->db->trans_commit(); 
			if($res->status == '1'){
				$this->session->set_flashdata('msg_status', 'Result set disabled successfully');
			}else{
				$this->session->set_flashdata('msg_status', 'Result set enabled successfully');
			}
			redirect('admin/resultset');
			//$result[] = array('resSuccess' => 1, 'msg' => 'status updated successfully');
			//echo json_encode($result);
			exit();				
		}else{
			
			$this->db->trans_rollback(); 
			$this->session->set_flashdata('msg_status', 'Unable to update status');
			redirect('admin/resultset');
		
			//$result[] = array('resSuccess' => 2, 'msg' => 'Unable to update status');
			//echo json_encode($result);			
			exit();
		}	
	
	
	}
	
	
	public function detailresult($id){
		$this->load->library('Admin_acess');
		$data["menu"] = "viewresult";
		$data["detailres"] = $this->adminModel->detailRes($id);
		
		$this->load->view('_admin_shared/header',$data);	
		$this->load->view('_admin/detail_res_lst',$data);
		$this->load->view('_admin_shared/footer',$data);

	}
	
	
	
	public function students($yr=''){
		$this->load->library('Admin_acess');
		$data["menu"] = "students";
		
		//SELECT DISTINCT SUBSTRING(regd_no,1,2) as batch FROM `tbl_students` ORDER by batch
		$data['stu_year'] = $this->adminModel->getDistinctStudentYear();
		$data['sel_yr'] = $yr; 
		if(!empty($yr)){
			$data['students'] = $this->adminModel->getStudentList($yr);
		}else{
			$data['students'] = array();	
		}
		
		$this->load->view('_admin_shared/header',$data);	
		$this->load->view('_admin/student_list',$data);
		$this->load->view('_admin_shared/footer',$data);
		
		
		
	}
	
	
	
	public function deleteset(){ 
		$this->load->library('Admin_acess');
		$data["menu"] = "viewresult";
		$id = $this->input->post('id',TRUE);
		
		
		$this->db->trans_strict(TRUE);	
		$this->db->trans_begin();
		
			$this->adminModel->deleteResultDetails($id);
			$this->adminModel->deleteSet($id);


		if($this->db->trans_status() === TRUE){
			$this->db->trans_commit(); 
			$result[] = array('resSuccess' => 1, 'msg' => 'Set deleted successfully');
			echo json_encode($result);
			exit();				
			
		}else{
			 $this->db->trans_rollback(); 
			$result[] = array('resSuccess' => 2, 'msg' => 'Unable to delete Set');
			echo json_encode($result);			
			exit();
		}	

		
		
	}
	
	
	public function stugroup($id){
		
		$this->load->library('Admin_acess');
		$data["menu"] = "viewresult";
		
		$data['resultset'] = $this->adminModel->getResultSetById($id);
		$data['students'] = $this->adminModel->getStudentByResultId($id);
		
		$this->load->view('_admin_shared/header',$data);	
		$this->load->view('_admin/stu_group',$data);
		$this->load->view('_admin_shared/footer',$data);
		
	}
	
	
	public function deletesturesultset(){
		$this->load->library('Admin_acess');
		$data["menu"] = "viewresult";
		
		$this->db->trans_strict(TRUE);	
		$this->db->trans_begin();
		
		
		$result_id = $this->input->post('result_id',TRUE);
		$regd_no = $this->input->post('regd_no',TRUE);
		
			$this->adminModel->deleteStuResultSet($id);


		if($this->db->trans_status() === TRUE){
			$this->db->trans_commit(); 
			$result[] = array('resSuccess' => 1, 'msg' => 'Set deleted successfully');
			echo json_encode($result);
			exit();				
			
		}else{
			 $this->db->trans_rollback(); 
			$result[] = array('resSuccess' => 2, 'msg' => 'Unable to delete Set');
			echo json_encode($result);			
			exit();
		}	
		
	}
	
	
	public function viewresult($reg_no,$result_id){
		
		$this->load->library('Admin_acess');
		$data["menu"] = "viewresult";
		
		$data["result"] = $this->adminModel->getStuResult($reg_no,$result_id);
		$this->load->view('_admin/result',$data);
		
	}
	
	
	public function updateResult(){
		$this->load->library('Admin_acess');
		$data["menu"] = "importresult";
		
		
				
		$this->db->trans_strict(TRUE);	
		$this->db->trans_begin();
		
		$streams = $this->config->item('streams');
		
		$result_title = $this->input->post('result_title',TRUE);
		$stream = $this->input->post('stream',TRUE);
		$stream = $streams[$stream][0];
		$semester = $this->input->post('semester',TRUE);
		
		
		
		$publish_date = $this->input->post('publish_date',TRUE);
		
		
		$result_type =  $this->input->post('result_type',TRUE);
		$rechecking_allowed = $this->input->post('rechecking_allowed',TRUE);
		$signature = $this->input->post('signature',TRUE);
		$start_date = '';
		$end_date = '';
		if($rechecking_allowed){
			
			if(!empty($_POST['start_date'])){
				$start_date = date('Y-m-d',strtotime($_POST['start_date']));
			}
			
			if(!empty($_POST['end_date'])){
				$end_date = date('Y-m-d',strtotime($_POST['end_date']));
			}
			
			
			
		}
		
		//echo $publish_date .'<br/>';
		//echo date('Y-m-d',strtotime($publish_date)) . '<br/>';
		//echo $start_date . '<br/>';
		//echo $end_date . '<br/>';
		//die();
		
		
		
		$recid = $this->input->post('recid',TRUE);
		$task  = $this->input->post('task',TRUE);
		
		
		$res_dt = array('title' => addslashes($result_title),
					  'stream' => $stream,
					  'semester' => $semester,
					  'publish_date' =>  date('Y-m-d',strtotime($publish_date)),
					  'rechecking_allowed' => $rechecking_allowed,
					  'start_date' => !empty($start_date)?$start_date:NULL,
					  'end_date' => !empty($end_date)?$end_date:NULL,
					  'signature' => $signature);
		
		
		//print_r($res_dt); die();
		
		$this->adminModel->updateResult($res_dt,$recid);
		
		if($this->db->trans_status() === TRUE){
			$this->db->trans_commit(); 
			$result[] = array('resSuccess' => 1, 'msg' => 'Result saved successfully');
			echo json_encode($result);
			exit();				
			
		}else{
			 $this->db->trans_rollback(); 
			$result[] = array('resSuccess' => 2, 'msg' => 'Unable to save result');
			echo json_encode($result);			
			exit();
		}	
		
		
		
		
		
	}
	
	
	
	public function getstudentbyresid(){
		$this->load->library('Admin_acess');
		$res_id = $this->input->get('res_id',TRUE);
		$data['res_id'] = $res_id;
		$data['students'] = $this->adminModel->getstudentbyresid($res_id);
		
		$stu_list = $this->load->view('_admin/stubyresid',$data,TRUE);
		
		echo $stu_list;
		
	}
	
	public function getstudentbyregno(){
		$this->load->library('Admin_acess');
		$regd_no = $this->input->get('rollno',TRUE);
		if(empty($regd_no)) {
			//$result[] = array('resSuccess' => 2, 'msg' => 'Invalid Request');
			//echo json_encode($result);
			echo 'Invalid Request';
			exit();
		}
		$data['regd_no'] = $regd_no;
		$data['student'] = $this->adminModel->getStudent($regd_no);
		if(empty($data['student'])) {
			//$result[] = array('resSuccess' => 2, 'msg' => 'Data not found');
			//echo json_encode($result);
			echo 'Data not found';
			exit();
		}
		
		//$result[] = array('resSuccess' => 1, 'msg' => '', 'content' => array("regd_no"=> $data['student']->regd_no ,"name"=> $data['student']->name) );
		//echo json_encode($result);
		echo $this->load->view('_admin/student_edit',$data, true);
		exit();
	
	}
	
	public function savestudent(){
		$this->load->library('Admin_acess');
		$regd_no = $this->input->post('regd_no',TRUE);

		if(empty($regd_no)) {
			$result[] = array('resSuccess' => 2, 'msg' => 'Invalid Request');
			echo json_encode($result);
			exit();
		}
		$data['regd_no'] = $regd_no;
		$data['student'] = $this->adminModel->getStudent($regd_no);
		if(empty($data['student'])) {
			$result[] = array('resSuccess' => 2, 'msg' => 'Data not found');
			echo json_encode($result);
			echo 'Data not found';
			exit();
		}
		
		$name = $this->input->post('name',TRUE);
		if(empty($name)) {
			$result[] = array('resSuccess' => 2, 'msg' => 'Student Name cannot be empty');
			echo json_encode($result);
			exit();
		}
		$name = strtoupper(trim($name));
		
		$updtData = array(
			'name' => $name
		);
		
		$this->adminModel->updateStudent($regd_no, $updtData);
		$data['student'] = $this->adminModel->getStudent($regd_no);
		
		$result[] = array('resSuccess' => 1, 'msg' => 'Student Name has been successfully updated', 'content' => array('regd_no' => $data['student']->regd_no, 'name' => $data['student']->name) );
		echo json_encode($result);
	}

	
	
	/************[ Rechecking/Retotaling ]******************/
	
	public function rechecking(){
		
		$this->load->library('Admin_acess');
		$data['menu'] = 'rechecking';
		$data['results'] =   $this->adminModel->recheckingResult();

		$this->load->view('_admin_shared/header',$data);	
		$this->load->view('_admin/rechecking',$data);
		$this->load->view('_admin_shared/footer',$data);
		
		
	}
	
	
	public function payments($id){
		
		$this->load->library('Admin_acess');
		$data['menu'] = 'rechecking';
		$data['result'] = 	  $this->adminModel->getResultSetById(md5($id)); 	
		$data['payments'] =   $this->adminModel->getPaymentsByResId($id,1);
		$data['failedpayments'] =   $this->adminModel->getPaymentsByResId($id,0);
		$data['failedpayments2'] =   $this->adminModel->getPaymentsByResId($id,2);
		$data['id'] = $id;
		#var_dump($data);
		$this->load->view('_admin_shared/header',$data);	
		$this->load->view('_admin/payments',$data);
		$this->load->view('_admin_shared/footer',$data);
		
	
	
	}
	
	
	public function markaspiad(){
		
		$this->load->library('Admin_acess');
		$data['menu'] = 'rechecking';
		
		$payment_id = $this->input->post('pay_id',TRUE);
		$tnx_ref_no = $this->input->post('tnx_ref_no',TRUE);
		$tnx_amount = $this->input->post('tnx_amount',TRUE);
		$remarks = $this->input->post('remarks',TRUE);
		
		$this->db->trans_strict(TRUE);	
		$this->db->trans_begin();
		
		
			$data_pay = array('tnx_reference_no' => addslashes($tnx_ref_no),
							  'manually_approved' => '1',
							  'payment_status' => '1',
							  'manual_approved_date' => date('Y-m-d'),
							  'payment_amount' =>addslashes($tnx_amount),
							  'remarks' => addslashes($remarks));

			$this->adminModel->updatePayments($payment_id,$data_pay);
		
		
		if($this->db->trans_status() === TRUE){
			$this->db->trans_commit(); 
			$result[] = array('resSuccess' => 1, 'msg' => 'Payments updated successfully');
			echo json_encode($result);
			exit();				
			
		}else{
			 $this->db->trans_rollback(); 
			$result[] = array('resSuccess' => 2, 'msg' => 'Unable to update payments');
			echo json_encode($result);			
			exit();
		}	
		
	}
	
	public function markaspending(){
	    $this->load->library('Admin_acess');
		$data['menu'] = 'rechecking';
		//print_r($_POST);
		$payment_id = $this->input->post('id',TRUE);
		
		$this->db->trans_strict(TRUE);	
		$this->db->trans_begin();
		
		$data_pay = array('payment_status' => '0');
    	$this->adminModel->updatePayments($payment_id,$data_pay);
		
		if($this->db->trans_status() === TRUE){
			$this->db->trans_commit(); 
			$result[] = array('resSuccess' => 1, 'msg' => 'Payments updated successfully');
			echo json_encode($result);
			exit();				
			
		}else{
			 $this->db->trans_rollback(); 
			$result[] = array('resSuccess' => 2, 'msg' => 'Unable to update payments');
			echo json_encode($result);			
			exit();
		}	
		
	}
	
	
	public function paymentdetails($id){
		
		//echo $id; die('-----');
		$this->load->library('Admin_acess');
		$data['menu'] = 'rechecking';

		$data['subjects'] = $this->adminModel->getpaymentsubpayId($id);
		$data['payments'] =	$this->adminModel->paymentdetails($id);
		if(empty($data['payments'])){
			redirect('admin/rechecking', 'refresh');
		}
	
		//print_r($data['payments']); die();
		//print_r($data['subjects']); die();	
		
		$this->load->view('_admin_shared/header',$data);	
		$this->load->view('_admin/paymentssubject',$data);
		$this->load->view('_admin_shared/footer',$data);
		
		
	}
	
	
	public function consolidated($id){
		$this->load->library('Admin_acess');
		$data['menu'] = 'rechecking';
		$data['result'] = 	  $this->adminModel->getResultSetById(md5($id)); 
		$data['consolidated'] = $this->adminModel->consolidatedResult($id);
		$data['id'] = $id;
		//print_r($data['consolidated']); die();

		$this->load->view('_admin_shared/header',$data);	
		$this->load->view('_admin/consolidated',$data);
		$this->load->view('_admin_shared/footer',$data);
		
	}
	
	
	public function consolidatedexcel($id){
		
		$spreadsheet = new Spreadsheet(); // instantiate Spreadsheet
		$sheet = $spreadsheet->getActiveSheet();
		
		$data['result'] = 	  $this->adminModel->getResultSetById(md5($id)); 
		$consolidated = $this->adminModel->consolidatedResult($id);
		
		
		
		$sheet->setCellValue('A1', 'Title');
		$sheet->setCellValue('B1', $data['result']->title);
		
		$sheet->setCellValue('A2', 'Stream');
		$sheet->setCellValue('B2', $data['result']->stream);
		
		
		$sheet->setCellValue('A3', 'Semester');
		$sheet->setCellValue('B3', $data['result']->semester);

		
		//print_r($dir); die();
		$table_columns = array("Regd No",
							   "Student Name",
							   "Subject Code",
							   "Subject Name");
		
		//Generate The Headers Here
		$sheet->fromArray($table_columns, NULL, 'A5');
		
		$spreadsheet
			->getActiveSheet()
			->getStyle('A1:A3')
			->getFill()
			->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
			->getStartColor()
			->setARGB('C0C0C0');
			
		$spreadsheet
			->getActiveSheet()
			->getStyle('A5:D5')
			->getFill()
			->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
			->getStartColor()
			->setARGB('C0C0C0');
		
		$row_no = 6;
		foreach($consolidated as $con){
			//echo $value->CompanyName; die('hello');
			$sheet->setCellValue('A' . $row_no, trim($con->regd_no));
			$sheet->setCellValue('B' . $row_no, trim($con->name));
			$sheet->setCellValue('C' . $row_no, trim($con->subject_code));
			$sheet->setCellValue('D' . $row_no, trim($con->subject_name));
			$row_no++;
		}
		
		
		//Will set the dimensioon to text width
		foreach (range('A','D') as $col) {
 			 $sheet->getColumnDimension($col)->setAutoSize(true);  
		}
		
		$writer = new Xlsx($spreadsheet);
		//$filename = 'name-of-the-generated-file.xlsx';

		$filename = 'consolidated_result_' . date('d-m-Y');

		header('Content-Type: application/vnd.ms-excel');
		header('Content-Disposition: attachment;filename="'. $filename .'.xlsx"'); 
		header('Cache-Control: max-age=0');
		
		$writer->save('php://output'); // download file 		
		
	}
	
	
	
	public function exportpaymentpaid($id){
		
		$spreadsheet = new Spreadsheet(); // instantiate Spreadsheet
		$sheet = $spreadsheet->getActiveSheet();
		
		$data['result'] = $this->adminModel->getResultSetById(md5($id)); 
		$payments = $this->adminModel->ExportPaymentsByResId($id);
		
		$sheet->setCellValue('A1', 'Title');
		$sheet->setCellValue('B1', $data['result']->title);
		
		$sheet->setCellValue('A2', 'Stream');
		$sheet->setCellValue('B2', $data['result']->stream);
		
		$sheet->setCellValue('A3', 'Semester');
		$sheet->setCellValue('B3', $data['result']->semester);
		
		$table_columns = array("Regd No",
							   "Student Name",
							   "Date of Submission",
							   "Amount",
							   "Status",
							   "Transaction Reference Number",
							   "Manually Approved",
							   "Manual Approve Date",
							   "Remarks");
		
		//Generate The Headers Here
		$sheet->fromArray($table_columns, NULL, 'A5');
		
		$spreadsheet
			->getActiveSheet()
			->getStyle('A1:A3')
			->getFill()
			->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
			->getStartColor()
			->setARGB('C0C0C0');

		
		$spreadsheet
			->getActiveSheet()
			->getStyle('A5:I5')
			->getFill()
			->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
			->getStartColor()
			->setARGB('C0C0C0');
		
		
		
		$row_no = 6;
		$sheet->getStyle('D')->getNumberFormat()->setFormatCode('0.00');
		$sheet->getStyle('D')->getAlignment()->setHorizontal('right');
				
		foreach($payments as $pay){

			$sheet->setCellValue('A' . $row_no, trim($pay->regd_no));
			$sheet->setCellValue('B' . $row_no, trim($pay->name));
			$sheet->setCellValue('C' . $row_no, date('d-m-Y',strtotime($pay->payment_date)));
			$sheet->setCellValue('D' . $row_no, trim(number_format($pay->payment_amount, 2)));
			$sheet->setCellValue('E' . $row_no, $pay->payment_status == '1'?'Paid':'Failed');
			$sheet->setCellValue('F' . $row_no, trim($pay->tnx_reference_no));
			$sheet->setCellValue('G' . $row_no, $pay->manually_approved == '1'?'Yes':'No');
			$sheet->setCellValue('H' . $row_no, !empty($pay->manual_approved_date)?date('d-m-Y',strtotime($pay->payment_date)):'');
			$sheet->setCellValue('I' . $row_no, trim($pay->Remarks));
			$row_no++;
		}
		
		
		//Will set the dimensioon to text width
		foreach (range('A','I') as $col) {
 			 $sheet->getColumnDimension($col)->setAutoSize(true);  
		}
		
		$writer = new Xlsx($spreadsheet);
		//$filename = 'name-of-the-generated-file.xlsx';

		$filename = 'all_payment_paid_' . date('d-m-Y');

		header('Content-Type: application/vnd.ms-excel');
		header('Content-Disposition: attachment;filename="'. $filename .'.xlsx"'); 
		header('Cache-Control: max-age=0');
		
		$writer->save('php://output'); // download file 			
	}
	
	
	public function exportpaymentfailed($id){
		
		$spreadsheet = new Spreadsheet(); // instantiate Spreadsheet
		$sheet = $spreadsheet->getActiveSheet();
		
		$data['result'] = $this->adminModel->getResultSetById(md5($id)); 
		$payments = $this->adminModel->exportPaymentsFailedByResId($id);
		
		$sheet->setCellValue('A1', 'Title');
		$sheet->setCellValue('B1', $data['result']->title);
		
		$sheet->setCellValue('A2', 'Stream');
		$sheet->setCellValue('B2', $data['result']->stream);
		
		$sheet->setCellValue('A3', 'Semester');
		$sheet->setCellValue('B3', $data['result']->semester);
		
		$table_columns = array("Regd No",
							   "Student Name",
							   "Date of Submission",
							   "Amount",
							   "Status",
							   "Transaction Reference Number",
							   "Manually Approved",
							   "Manual Approve Date",
							   "Remarks");
		
		//Generate The Headers Here
		$sheet->fromArray($table_columns, NULL, 'A5');
		
		$spreadsheet
			->getActiveSheet()
			->getStyle('A1:A3')
			->getFill()
			->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
			->getStartColor()
			->setARGB('C0C0C0');

		
		$spreadsheet
			->getActiveSheet()
			->getStyle('A5:I5')
			->getFill()
			->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
			->getStartColor()
			->setARGB('C0C0C0');
		
		
		
		$row_no = 6;
		
		$sheet->getStyle("D")->getNumberFormat()->setFormatCode('0.00');
		$sheet->getStyle('D')->getAlignment()->setHorizontal('right');
		
		foreach($payments as $pay){
			//echo $value->CompanyName; die('hello');
			$sheet->setCellValue('A' . $row_no, trim($pay->regd_no));
			$sheet->setCellValue('B' . $row_no, trim($pay->name));
			$sheet->setCellValue('C' . $row_no, date('d-m-Y',strtotime($pay->payment_date)));
			$sheet->setCellValue('D' . $row_no, trim(number_format($pay->payment_amount, 2)));
			$sheet->setCellValue('E' . $row_no, $pay->payment_status == '1'?'Success':'Failed');
			$sheet->setCellValue('F' . $row_no, trim($pay->tnx_reference_no));
			$sheet->setCellValue('G' . $row_no, $pay->manually_approved == '1'?'Yes':'No');
			$sheet->setCellValue('H' . $row_no, !empty($pay->manual_approved_date)?date('d-m-Y',strtotime($pay->payment_date)):'');
			$sheet->setCellValue('I' . $row_no, trim($pay->Remarks));
			$row_no++;
		}
		
		
		//Will set the dimensioon to text width
		foreach (range('A','I') as $col) {
 			 $sheet->getColumnDimension($col)->setAutoSize(true);  
		}
		
		$writer = new Xlsx($spreadsheet);
		//$filename = 'name-of-the-generated-file.xlsx';

		$filename = 'all_payment_failed_' . date('d-m-Y');

		header('Content-Type: application/vnd.ms-excel');
		header('Content-Disposition: attachment;filename="'. $filename .'.xlsx"'); 
		header('Cache-Control: max-age=0');
		
		$writer->save('php://output'); // download file 			
	}
	
	public function exportpaymentsubjects($resultid){
		
		$spreadsheet = new Spreadsheet(); // instantiate Spreadsheet
		$sheet = $spreadsheet->getActiveSheet();
		
		$data['result'] = $this->adminModel->getResultSetById(md5($resultid)); 		
		$paymentsubjects = $this->adminModel->exportPaymentSubjectsByResId($resultid);
				
		$sheet->setCellValue('A1', 'Title');
		$sheet->setCellValue('B1', $data['result']->title);
		
		$sheet->setCellValue('A2', 'Stream');
		$sheet->setCellValue('B2', $data['result']->stream);
		
		$sheet->setCellValue('A3', 'Semester');
		$sheet->setCellValue('B3', $data['result']->semester);
		
		$table_columns = array("Regd No",
							   "Student Name",
							   "Subject Code",
							   "Subject Name",
							   "Rechecking Type",
							   "Amount",
							   "Status",
							   "Date of Submission");
		
		//Generate The Headers Here 
		$sheet->fromArray($table_columns, NULL, 'A5');
		
		$spreadsheet
			->getActiveSheet()
			->getStyle('A1:A3')
			->getFill()
			->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
			->getStartColor()
			->setARGB('C0C0C0');

		
		$spreadsheet
			->getActiveSheet()
			->getStyle('A5:H5')
			->getFill()
			->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
			->getStartColor()
			->setARGB('C0C0C0');
		
		$row_no = 6;
		$sheet->getStyle("F")->getNumberFormat()->setFormatCode('0.00');
		$sheet->getStyle('F')->getAlignment()->setHorizontal('right'); 
		
		foreach($paymentsubjects as $paysub){
			
			$pay_amt = 0;
			if($paysub->payment_status == 1){
				if($paysub->rechecking_type == 1){
					$pay_amt = 500;
				}else{
					$pay_amt = 1000;
				}
			}else{
				$pay_amt = $paysub->payment_amount;
			}

			$sheet->setCellValue('A' . $row_no, trim($paysub->regd_no));
			$sheet->setCellValue('B' . $row_no, trim($paysub->name));			
			$sheet->setCellValue('C' . $row_no, $paysub->subject_code);
			$sheet->setCellValue('D' . $row_no, trim($paysub->subject_name));
			$sheet->setCellValue('E' . $row_no, $paysub->rechecking_type == '1'?'Rechecking/Retotaling':'Rechecking/Retotaling with photocopy');			
			$sheet->setCellValue('F' . $row_no, number_format($pay_amt,2));
			$sheet->setCellValue('G' . $row_no, $paysub->payment_status == '1'?'Paid':'Failed');
			$sheet->setCellValue('H' . $row_no, $paysub->payment_date);			
			$row_no++;
		}
		
		
		//Will set the dimensioon to text width
		foreach (range('A','H') as $col) {
 			 $sheet->getColumnDimension($col)->setAutoSize(true);  
		}
		
		$writer = new Xlsx($spreadsheet);
		//$filename = 'name-of-the-generated-file.xlsx';

		$filename = 'all_subject_payment_status_' . date('d-m-Y');

		header('Content-Type: application/vnd.ms-excel');
		header('Content-Disposition: attachment;filename="'. $filename .'.xlsx"'); 
		header('Cache-Control: max-age=0');
		
		$writer->save('php://output'); // download file 		
	
	}
	
	public function exportresultset($resultid){
				
		$spreadsheet = new Spreadsheet(); // instantiate Spreadsheet
		$sheet = $spreadsheet->getActiveSheet();
		
		$resultset = $this->adminModel->exportResultSet($resultid);		
								
		$table_columns = array("BRANCH",
							   "REG_NO",
							   "STD_NAME",
							   "E_mail",
							   "SUB_CODE",
							   "SUBJECT",
							   "Credit",
							   "Grade",
							   "SGPA",
							   "Type");							   
		
		//Generate The Headers Here 
		$sheet->fromArray($table_columns, NULL, 'A1');
						
		$spreadsheet
			->getActiveSheet()
			->getStyle('A1:J1')
			->getFill()
			->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
			->getStartColor()
			->setARGB('C0C0C0');
		
		$row_no = 2;
		//$sheet->getStyle("G")->getNumberFormat()->setFormatCode('0.00');
		$sheet->getStyle('H')->getAlignment()->setHorizontal('center');
		$sheet->getStyle('J')->getAlignment()->setHorizontal('center');
		
		foreach($resultset as $rset){

			$sheet->setCellValue('A' . $row_no, trim($rset->branch));
			$sheet->setCellValue('B' . $row_no, trim($rset->regd_no));			
			$sheet->setCellValue('C' . $row_no, $rset->name);
			$sheet->setCellValue('D' . $row_no, $rset->email);
			$sheet->setCellValue('E' . $row_no, $rset->subject_code);			
			$sheet->setCellValue('F' . $row_no, $rset->subject_name);
			$sheet->setCellValue('G' . $row_no, $rset->credits);
			$sheet->setCellValue('H' . $row_no, $rset->grade);
			$sheet->setCellValue('I' . $row_no, $rset->sgpa);
			$sheet->setCellValue('J' . $row_no, $rset->subject_type);			
			$row_no++;
		}
		
		
		//Will set the dimensioon to text width
		foreach (range('A','I') as $col) {
 			 $sheet->getColumnDimension($col)->setAutoSize(true);  
		}
		
		$writer = new Xlsx($spreadsheet);
		//$filename = 'name-of-the-generated-file.xlsx';

		$filename = $resultset[0]->title;

		header('Content-Type: application/vnd.ms-excel');
		header('Content-Disposition: attachment;filename="'. $filename .'.xlsx"'); 
		header('Cache-Control: max-age=0');
		
		$writer->save('php://output'); // download file 
				
		
	}
	
	public function exportstudent($res_id){
		
		$spreadsheet = new Spreadsheet(); // instantiate Spreadsheet
		$sheet = $spreadsheet->getActiveSheet();
		
		$data['result'] = 	  $this->adminModel->getResultSetById(md5($res_id)); 
		$students = $this->adminModel->getstudentbyresid($res_id);
		
		
		
		$sheet->setCellValue('A1', 'Title');
		$sheet->setCellValue('B1', $data['result']->title);
		
		$sheet->setCellValue('A2', 'Stream');
		$sheet->setCellValue('B2', $data['result']->stream);
		
		
		$sheet->setCellValue('A3', 'Semester');
		$sheet->setCellValue('B3', $data['result']->semester);
		
		
		//print_r($dir); die();
		$table_columns = array("Regd No",
							   "Student",
							   "Email",
							   "Password");
		

		//Generate The Headers Here
		$sheet->fromArray($table_columns, NULL, 'A5');
		
		
		$spreadsheet
			->getActiveSheet()
			->getStyle('A1:A3')
			->getFill()
			->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
			->getStartColor()
			->setARGB('C0C0C0');
			
			
			
		$spreadsheet
			->getActiveSheet()
			->getStyle('A5:D5')
			->getFill()
			->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
			->getStartColor()
			->setARGB('C0C0C0');
		
		
		
		
		
		$row_no = 6;
		foreach($students as $stu){
			//echo $value->CompanyName; die('hello');
			$sheet->setCellValue('A' . $row_no, trim($stu->regd_no));
			$sheet->setCellValue('B' . $row_no, trim($stu->name));
			$sheet->setCellValue('C' . $row_no, trim($stu->email));
			$sheet->setCellValue('D' . $row_no, trim($this->encryption->decrypt($stu->password)));
			$row_no++;
		}
		
		
		//Will set the dimensioon to text width
		foreach (range('A','D') as $col) {
 			 $sheet->getColumnDimension($col)->setAutoSize(true);  
		}
		
		$writer = new Xlsx($spreadsheet);
		//$filename = 'name-of-the-generated-file.xlsx';
		$filename = 'Student_list_' . str_replace(' ', '_', $data['result']->title);

		$filename = $filename . '_' . date('d-m-Y');

		header('Content-Type: application/vnd.ms-excel');
		header('Content-Disposition: attachment;filename="'. $filename .'.xlsx"'); 
		header('Cache-Control: max-age=0');
		
		$writer->save('php://output'); // download file 		
		
	}	
	
	
	

}
