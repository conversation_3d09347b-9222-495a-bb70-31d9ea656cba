/**
 * Bamanankan (bm) translation for bootstrap-datepicker
 * <PERSON><PERSON> Fall <<EMAIL>>
 */
;(function($){
  $.fn.datepicker.dates['bm'] = {
    days: ["<PERSON><PERSON>","Ntɛnɛn","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>"],
    daysShort: ["Kar","Ntɛ","<PERSON>r","<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","Sib"],
    daysMin: ["<PERSON>","Nt","<PERSON>","Ar","<PERSON>","<PERSON>","<PERSON>"],
    months: ["<PERSON><PERSON><PERSON><PERSON><PERSON>o","<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>o","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>","ɔkut<PERSON>burukalo","Now<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON>"],
    monthsShort: ["<PERSON><PERSON>","<PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","ɔ<PERSON>","<PERSON>","<PERSON>"],
    today: "<PERSON><PERSON>",
    monthsTitle: "<PERSON><PERSON>",
    clear: "<PERSON> jɔsi",
    weekStart: 1,
    format: "dd/mm/yyyy"
  };
}(jQuery));
