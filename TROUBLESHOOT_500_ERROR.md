# Troubleshooting 500 Error for Student Forgot Password

## Diagnostic Steps

### 1. Check Database Structure
Visit: `http://localhost/outr_results/student/test_db`

This will show:
- Whether the `token` field exists in `tbl_students` table
- Complete table structure

**Expected Response:**
```json
{
  "token_field_exists": true,
  "table_structure": [...]
}
```

### 2. If Token Field Missing
Run this SQL command in your database:

```sql
ALTER TABLE `tbl_students` ADD COLUMN `token` VARCHAR(255) NULL DEFAULT NULL AFTER `status`;
ALTER TABLE `tbl_students` ADD INDEX `idx_token` (`token`);
```

### 3. Check PHP Error Logs
Look for detailed error messages in:
- Apache error log
- PHP error log
- CodeIgniter logs (application/logs/)

### 4. Common Issues and Fixes

#### Issue 1: Token Field Missing
**Symptom**: 500 error when calling sendpwd
**Solution**: Run the database migration SQL above

#### Issue 2: Model Method Missing
**Symptom**: Call to undefined method
**Solution**: Check if all methods exist in Student_model.php:
- `getStudentByRegdNo()`
- `updateToken()`

#### Issue 3: Database Connection
**Symptom**: Database connection error
**Solution**: Check database configuration in `application/config/database.php`

#### Issue 4: CodeIgniter Libraries
**Symptom**: Class not found errors
**Solution**: Check if required libraries are loaded in constructor

## Fixed Issues in Latest Update

### ✅ Controller Fixes Applied:
1. **Initialized `$result` array** - was causing undefined variable error
2. **Added try-catch block** - proper exception handling
3. **Added input validation** - prevents empty data processing
4. **Added JSON content-type header** - ensures proper response format

### ✅ Code Structure:
```php
public function sendpwd()
{
    header('Content-Type: application/json');
    $result = array(); // Fixed: Initialize result array
    
    try {
        $regdno = $this->input->post('regdno', TRUE);
        
        if (empty($regdno)) {
            $result[] = array('resSuccess' => 2, 'msg' => 'Registration number is required.');
            echo json_encode($result);
            exit();
        }
        
        $student_dt = $this->studentModel->getStudentByRegdNo($regdno);
        // ... rest of the logic
        
    } catch (Exception $e) {
        $result[] = array('resSuccess' => 2, 'msg' => 'An error occurred: ' . $e->getMessage());
        echo json_encode($result);
        exit();
    }
}
```

## Testing Steps

### Step 1: Database Check
1. Visit: `http://localhost/outr_results/student/test_db`
2. Verify token field exists
3. If not, run the SQL migration

### Step 2: Test Forgot Password
1. Visit: `http://localhost/outr_results/student/forgotpwd`
2. Enter a valid registration number
3. Submit form
4. Check browser network tab for response

### Step 3: Check Error Details
If still getting 500 error:
1. Enable CodeIgniter error reporting
2. Check PHP error logs
3. Look at browser network tab for detailed error

## Quick Fix Commands

### Enable Error Reporting (for debugging):
Add to `index.php`:
```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

### Check CodeIgniter Environment:
In `index.php`, set:
```php
define('ENVIRONMENT', 'development');
```

The 500 error should now be resolved with proper error handling and initialization fixes.
