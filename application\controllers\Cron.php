<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Cron extends CI_Controller
{
     public $db;
     function __construct()
     {
          parent::__construct();
          $this->load->database(); // Load database
          $this->load->model('student_model', 'studentModel');
     }

     public function index()
     {
          $this->load->model('rechecking_model', 'recheckingModel');
          #echo "Start<br/>";
          set_time_limit(130);
          $curlSess = null;

          $markTimeoutTime = date('Y-m-d H:i:s', strtotime("-73 hours"));
          $notFoundTime = date('Y-m-d H:i:s', strtotime("-130 minutes"));
          $fromTime = date('Y-m-d H:i:s', strtotime("-131 days"));
          $toTime = date('Y-m-d H:i:s', strtotime("-31 minutes"));
          $toTime2 = date('Y-m-d H:i:s', strtotime("-1 minute"));

          $rnum = rand(0, 1);
          $sortOrder = $rnum == 0 ? 'asc' : 'desc';
          $limit = 13;

          // CodeIgniter 3 query syntax
          $this->db->select('id, regd_no, result_id, payment_date, payment_amount, payment_status, tnx_reference_no, manually_approved, manual_approved_date, remarks,txn_no,txn_msg,created_at,updated_at,error_message');
          $this->db->from('tbl_payments');
          $this->db->where('payment_status', 0);
          $this->db->where('txn_no IS NOT NULL');
          $this->db->where('txn_no !=', '');
          $this->db->where('created_at >=', $fromTime);
          $this->db->where('created_at <=', $toTime);
          $this->db->order_by('id', $sortOrder);
          $this->db->limit($limit);
          $tranxlist = $this->db->get()->result();

          // $sql = $this->db->last_query();
          // echo "SQL Query: " . $sql . "<br/><br/>";
          // echo "Results:<br/>";
          // echo "<pre>";
          // print_r($tranxlist);
          // echo "</pre>";
          if (empty($tranxlist)) {
               echo "No records found.";
          } else {
               foreach ($tranxlist as $tranx) {
                    usleep(310);
                    if (isset($curlSess)) {
                         unset($curlSess);
                    }
                    echo "Processing transaction for: " . $tranx->regd_no . "<br>";

                    $arrQueryMsg = [
                         'RequestType' => '0122',
                         'MerchantID' => 'COLLEGEET',
                         //'CustomerID' => str_replace('RS','',$tranx->txn_no),
                         'CustomerID' => $tranx->txn_no,
                         'ts' => date('YmdHis'),
                    ];


                    $str = implode("|", $arrQueryMsg);
                    $checksum = hash_hmac('sha256', $str, 'blnbqVUlfb9C', false);
                    $checksum = strtoupper($checksum);
                    $final_msg = $str . "|" . $checksum;

                    $api_url = 'https://www.billdesk.com/pgidsk/PGIQueryController';

                    $curlSess = curl_init();
                    curl_setopt($curlSess, CURLOPT_URL, $api_url . '/?msg=' . $final_msg);
                    curl_setopt($curlSess, CURLOPT_RETURNTRANSFER, 1);
                    curl_setopt($curlSess, CURLOPT_SSL_VERIFYPEER, false);
                    curl_setopt($curlSess, CURLOPT_SSL_VERIFYHOST, false);
                    $content = curl_exec($curlSess);
                    var_dump($content);

                    if (empty($content)) {
                         $curl_err = curl_error($curlSess);
                         if (!empty($curl_err)) {
                              $res = array("status" => 1, "error" => 1, "messages" => array("error" => $curl_err));
                         } else {
                              $res = array("status" => 3, "error" => 3, "messages" => array("error" => "Unable to get data from API"));
                         }
                         echo "\n<br>Response error: " . $res["messages"]["error"] . "\n<br>";
                         continue;
                    } else {
                         $final_msg_exp = explode("|", $content);
                         $code = substr(strrchr($content, "|"), 1); // Last checksum value
                         $str = str_replace("|" . $code, "", $content); // String replace with empty space
                         $checksumRes = hash_hmac('sha256', $str,  'blnbqVUlfb9C', false); // Calculated checksum
                         $checksumRes = strtoupper($checksumRes);

                         if (!empty($final_msg_exp) && !empty($final_msg_exp[25])) {
                              echo "Response message: " . $final_msg_exp[25] . "\n<br>";

                              // Handle "transaction not found" and other special cases
                              if (!empty($final_msg_exp[25]) && in_array(strtolower($final_msg_exp[25]), ['transaction not found', 'collect expired', 'debit failed', 'credit failed']) && $checksumRes == $code) {
                                   if ($tranx->created_at < $notFoundTime) {
                                        $tranxData = [
                                             'payment_status' => 2,
                                             'error_message' => $final_msg_exp[25],
                                             'tnx_reference_no' => $final_msg_exp[3],
                                             'updated_at' => date('Y-m-d H:i:s'),
                                        ];
                                        $this->recheckingModel->updatePayment($tranxData, $tranx->id);
                                   } else {
                                        $tranxData = [
                                             'payment_status' => 0,
                                             'error_message' => $final_msg_exp[25],
                                             'tnx_reference_no' => $final_msg_exp[3],
                                             'updated_at' => date('Y-m-d H:i:s'),
                                        ];
                                        $this->recheckingModel->updatePayment($tranxData, $tranx->id);
                                   }
                                   continue;
                              } else {



                                   // Process valid transaction details
                                   $txnVals = [
                                        'RequestType' => $final_msg_exp[0],
                                        'MerchantID' => $final_msg_exp[1],
                                        'CustomerID' => $final_msg_exp[2],
                                        'TxnReferenceNo' => $final_msg_exp[3],
                                        'BankReferenceNo' => $final_msg_exp[4],
                                        'TxnAmount' => $final_msg_exp[5],
                                        'BankID' => $final_msg_exp[6],
                                        'TxnType' => $final_msg_exp[8],
                                        'TxnDate' => $final_msg_exp[14],
                                        'AuthStatus' => $final_msg_exp[15],
                                        'AdditionalInfo1' => $final_msg_exp[17], // Student name
                                        'AdditionalInfo4' => $final_msg_exp[20], // Registration number
                                        'ErrorStatus' => $final_msg_exp[24],
                                        'ErrorDescription' => $final_msg_exp[25],
                                        'RefundStatus' => $final_msg_exp[27],
                                        'TotalRefundAmount' => $final_msg_exp[28],
                                        'LastRefundDate' => $final_msg_exp[29],
                                        'QueryStatus' => $final_msg_exp[31],
                                        'Checksum' => $final_msg_exp[32],
                                   ];

                                   $txnVals['txn_referenceno'] = $txnVals['TxnReferenceNo'];
                                   $txnVals['bank_referenceno'] = $txnVals['BankReferenceNo'];
                                   $txnVals['txn_amount'] = $txnVals['TxnAmount'];
                                   $txnVals['bdtxn_date'] = $txnVals['TxnDate'];
                                   $txnVals['txn_date'] = (!empty($txnVals['TxnDate']) && strlen($txnVals['TxnDate']) == 19 ?
                                        (substr($txnVals['TxnDate'], 6, 4) . '-' . substr($txnVals['TxnDate'], 3, 2) . '-' .
                                             substr($txnVals['TxnDate'], 0, 2) . substr($txnVals['TxnDate'], 10)) : date('Y-m-d H:i:s'));
                                   $txnVals['txn_status'] = $txnVals['AuthStatus'];
                                   $txnVals['student_name'] = $txnVals['AdditionalInfo1'];
                                   $txnVals['regd_no'] = $txnVals['AdditionalInfo4'];

                                   $status = null;
                                   if ($checksumRes == $code) {
                                        // Determine transaction status
                                        if (
                                             !empty($txnVals['AuthStatus']) && $txnVals['AuthStatus'] == '0300' &&
                                             !empty($txnVals['RefundStatus']) && $txnVals['RefundStatus'] == '0699'
                                        ) {
                                             $status = 2;
                                             $txnVals['txn_msg'] = "Payment status (0300) success but it has been refunded (0699) back to customer";
                                        } else if (
                                             !empty($txnVals['AuthStatus']) && $txnVals['AuthStatus'] == '0300' &&
                                             !empty($txnVals['RefundStatus']) && $txnVals['RefundStatus'] == '0799'
                                        ) {
                                             $status = 2;
                                             $txnVals['txn_msg'] = "Payment status (0300) success but a refund (0799) was processed for this transaction";
                                        } else if (
                                             !empty($txnVals['AuthStatus']) && $txnVals['AuthStatus'] == '0300' &&
                                             !empty($txnVals['RefundStatus']) && $txnVals['RefundStatus'] == 'NA'
                                        ) {
                                             $status = 1;
                                             $txnVals['txn_msg'] = $final_msg_exp[25]; // "Payment status (0300) success"
                                        } else if (
                                             !empty($txnVals['AuthStatus']) && $txnVals['AuthStatus'] == '0002' &&
                                             !empty($txnVals['RefundStatus']) && $txnVals['RefundStatus'] == 'NA' &&
                                             $tranx->created_at < $markTimeoutTime
                                        ) {
                                             $status = 2;
                                             $txnVals['txn_msg'] = "This transaction is not yet completed or was abandoned by the user.";
                                        } else if (
                                             !empty($txnVals['AuthStatus']) && $txnVals['AuthStatus'] == '0002' &&
                                             !empty($txnVals['RefundStatus']) && $txnVals['RefundStatus'] == 'NA'
                                        ) {
                                             $status = 0;
                                             $txnVals['txn_msg'] = "This transaction is not yet completed or was abandoned by the user.";
                                        } else if (
                                             !empty($txnVals['AuthStatus']) && $txnVals['AuthStatus'] == '0399' &&
                                             !empty($txnVals['RefundStatus']) && $txnVals['RefundStatus'] == 'NA'
                                        ) {
                                             $status = 2;
                                             $txnVals['txn_msg'] = "Failed transaction";
                                        } else if (
                                             !empty($txnVals['AuthStatus']) && $txnVals['AuthStatus'] == '0001' &&
                                             !empty($txnVals['RefundStatus']) && $txnVals['RefundStatus'] == 'NA'
                                        ) {
                                             $status = 2;
                                             $txnVals['txn_msg'] = "Failed transaction";
                                        } else {
                                             $status = 0;
                                             $txnVals['txn_msg'] = $final_msg_exp[25];
                                        }

                                        if (!is_null($status)) {
                                             $tranxData2 = [
                                                  'payment_status' => $status,
                                                  'tnx_reference_no' => $txnVals['txn_referenceno'],
                                                  'txn_msg' => $txnVals['txn_msg'],
                                                  'payment_amount' => $txnVals['txn_amount'],
                                                  'error_code' => $txnVals['txn_status'],
                                                  'error_message' => $txnVals['txn_msg'],
                                                  'updated_at' => date('Y-m-d H:i:s')
                                             ];
                                             echo "Transaction data to update: ";
                                             print_r($tranxData2);
                                             echo "\n<br>";
                                             try {
                                                  $this->db->trans_begin();

                                                  #$this->db->where('id', $tranx->id)->update('tbl_payments', $tranxData2);
                                                  $this->recheckingModel->updatePayment($tranxData2, $tranx->id);
                                                  if ($this->db->trans_status() === FALSE) {
                                                       $this->db->trans_rollback();
                                                  } else {
                                                       $this->db->trans_commit();
                                                  }
                                             } catch (Exception $e) {
                                                  echo "Error: " . $e->getMessage();
                                             }
                                        }
                                   }
                              }
                         }
                    }
               }
          }
          return "Payment status update process completed.";
     }
     
     public function test(){
        $arrQueryMsg = [
             'RequestType' => '0122',
             'MerchantID' => 'COLLEGEET',
             'CustomerID' => '212310004920250516121846',
             'ts' => date('YmdHis'),
        ];


        $str = implode("|", $arrQueryMsg);
        $checksum = hash_hmac('sha256', $str, 'blnbqVUlfb9C', false);
        $checksum = strtoupper($checksum);
        $final_msg = $str . "|" . $checksum;
        echo $final_msg."<br>";

        $api_url = 'https://www.billdesk.com/pgidsk/PGIQueryController';

        $curlSess = curl_init();
        curl_setopt($curlSess, CURLOPT_URL, $api_url . '/?msg=' . $final_msg);
        curl_setopt($curlSess, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curlSess, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curlSess, CURLOPT_SSL_VERIFYHOST, false);
        $content = curl_exec($curlSess);
        var_dump($content);

     }
}
