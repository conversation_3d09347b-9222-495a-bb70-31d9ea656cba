<!DOCTYPE html>
<html>

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title><?php echo APP_STUDENT_TITLE ?> | Student Reset Password</title>
	<!-- Tell the browser to be responsive to screen width -->
	<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
	<!-- Bootstrap 3.3.7 -->
	<link rel="stylesheet" href="<?php echo base_url('assets/bower_components/bootstrap/dist/css/bootstrap.min.css') ?>">
	<!-- Font Awesome -->
	<link rel="stylesheet" href="<?php echo base_url('assets/bower_components/font-awesome/css/font-awesome.min.css') ?>">
	<!-- Ionicons -->
	<link rel="stylesheet" href="<?php echo base_url('assets/bower_components/Ionicons/css/ionicons.min.css') ?>">
	<!-- Theme style -->
	<link rel="stylesheet" href="<?php echo base_url('assets/dist/css/AdminLTE.min.css') ?>">

	<!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
	<!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
	<!--[if lt IE 9]>
  <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
  <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
  <![endif]-->

	<!-- Google Font -->
	<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,600,700,300italic,400italic,600italic">
	<style>
		body {
			background: url("<?php echo base_url('assets/images/MAIN_GATE.jpg'); ?>") !important;
			background-size: cover !important;
			background-repeat: no-repeat;
		}

		.login-logo a {
			color: #fff;
		}
	</style>
</head>

<body class="hold-transition login-page">
	<div class="login-box">
		<div class="login-logo">
			<a href="<?php echo base_url('/') ?>"><b>STUDENT PORTAL<br /><span class="text-small">OUTR (Formerly CET) BHUBANESWAR</span></b></a>
		</div>
		<!-- /.login-logo -->
		<div class="login-box-body">
			<p class="login-box-msg"><strong>Reset Password</strong></p>

			<form id="frmReset" method="POST" novalidate>

				<div class="form-group has-feedback">
					<input type="password" id="newpassword" name="newpassword" autocomplete="off" class="form-control" placeholder="New Password*" required>
					<span class="glyphicon glyphicon-lock form-control-feedback"></span>
				</div>

				<div class="form-group has-feedback">
					<input type="password" id="confirmpassword" name="confirmpassword" autocomplete="off" class="form-control" placeholder="Confirm Password*" required>
					<span class="glyphicon glyphicon-lock form-control-feedback"></span>
				</div>
				<div class="row">
					<div class="col-sm-8">
						<a href="<?php echo base_url('student'); ?>">Back to Login</a>
					</div>
					<!-- /.col -->
					<div class="col-xs-4">
						<input type="hidden" id="regd_no" name="regd_no" value="<?php echo $student_res->regd_no ?>" />
						<input type="hidden" id="token" name="token" value="<?php echo $student_res->token ?>" />
						<input type="hidden" id="task" name="task" value="2" />
						<button type="submit" class="btn btn-primary btn-block btn-flat" id="btn-reset">Reset</button>
					</div>
					<!-- /.col -->
				</div>
			</form>

		</div>
		<!-- /.login-box-body -->
	</div>
	<!-- /.login-box -->

	<!-- jQuery 3 -->
	<script src="<?php echo base_url('assets/bower_components/jquery/dist/jquery.min.js') ?>"></script>
	<!-- Bootstrap 3.3.7 -->
	<script src="<?php echo base_url('assets/bower_components/bootstrap/dist/js/bootstrap.min.js') ?>"></script>
	<!-- AdminLTE App -->
	<script src="<?php echo base_url('assets/dist/js/adminlte.min.js') ?>"></script>

	<!-- Use CDN for bootbox to avoid MIME type issues -->
	<script src="https://cdnjs.cloudflare.com/ajax/libs/bootbox.js/5.5.2/bootbox.min.js"></script>

	<script>
		$(document).ready(function() {

			// Manual validation function
			function validateResetForm() {
				var newPassword = $('#newpassword').val().trim();
				var confirmPassword = $('#confirmpassword').val().trim();
				var errorMsg = '';

				// Clear previous errors
				$('.error-message').remove();
				$('#newpassword, #confirmpassword').removeClass('is-invalid');

				if (newPassword === '') {
					errorMsg = 'Please enter new password';
					$('#newpassword').addClass('is-invalid');
					$('#newpassword').after('<span class="error-message" style="color: #dc3545; font-size: 0.875em; display: block; margin-top: 5px;">' + errorMsg + '</span>');
					return false;
				} else if (newPassword.length < 6) {
					errorMsg = 'Password must be at least 6 characters';
					$('#newpassword').addClass('is-invalid');
					$('#newpassword').after('<span class="error-message" style="color: #dc3545; font-size: 0.875em; display: block; margin-top: 5px;">' + errorMsg + '</span>');
					return false;
				}

				if (confirmPassword === '') {
					errorMsg = 'Please confirm your password';
					$('#confirmpassword').addClass('is-invalid');
					$('#confirmpassword').after('<span class="error-message" style="color: #dc3545; font-size: 0.875em; display: block; margin-top: 5px;">' + errorMsg + '</span>');
					return false;
				} else if (newPassword !== confirmPassword) {
					errorMsg = 'Passwords do not match';
					$('#confirmpassword').addClass('is-invalid');
					$('#confirmpassword').after('<span class="error-message" style="color: #dc3545; font-size: 0.875em; display: block; margin-top: 5px;">' + errorMsg + '</span>');
					return false;
				}

				return true;
			}

			$('#frmReset').submit(function(e) {
				e.preventDefault();

				// Validate form
				if (!validateResetForm()) {
					return false;
				}

				$.ajax({
					type: "POST",
					url: "<?php echo base_url('student/updatepassword') ?>",
					data: $("#frmReset").serialize(),
					dataType: 'json',
					beforeSend: function(xhr) {
						$("#btn-reset").prop("disabled", true);
						$("#btn-reset").html("<i class='fa fa-spinner fa-spin'></i> Resetting...");
					},
					success: function(res_data) {
						$("#btn-reset").prop("disabled", false);
						$("#btn-reset").html("Reset");

						if (res_data && res_data[0]) {
							if (res_data[0].resSuccess == 1) {
								var successMsg = res_data[0].msg || 'Password changed successfully';
								if (typeof bootbox !== 'undefined') {
									bootbox.alert({
										size: "small",
										message: successMsg,
										callback: function() {
											window.location.href = "<?php echo base_url('student'); ?>";
										}
									});
								} else {
									alert(successMsg);
									window.location.href = "<?php echo base_url('student'); ?>";
								}
							} else if (res_data[0].resSuccess == 2) {
								var errorMsg = res_data[0].msg || 'An error occurred. Please try again.';
								if (typeof bootbox !== 'undefined') {
									bootbox.alert({
										size: "small",
										message: errorMsg
									});
								} else {
									alert(errorMsg);
								}
							}
						} else {
							alert('Invalid response from server. Please try again.');
						}
					},
					error: function(xhr, status, error) {
						$("#btn-reset").prop("disabled", false);
						$("#btn-reset").html("Reset");

						var errorMsg = 'An error occurred while resetting your password. Please try again.';
						if (typeof bootbox !== 'undefined') {
							bootbox.alert({
								size: "small",
								message: errorMsg
							});
						} else {
							alert(errorMsg);
						}
					}
				});

				return false;
			});
		});
	</script>
</body>

</html>