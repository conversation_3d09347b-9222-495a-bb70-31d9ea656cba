<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

ERROR - 2025-07-20 03:18:18 --> Query error: Column 'rechecking_type' cannot be null - Invalid query: INSERT INTO `tbl_payment_subjects` (`payment_id`, `rechecking_type`, `subject_code`, `subject_name`) VALUES (15747,NULL,'BH1461','PHY<PERSON>CS')
ERROR - 2025-07-20 16:52:55 --> Query error: Column 'rechecking_type' cannot be null - Invalid query: INSERT INTO `tbl_payment_subjects` (`payment_id`, `rechecking_type`, `subject_code`, `subject_name`) VALUES (15818,NULL,'BH1423','ENGLISH FOR TECHNICAL WRITING')
ERROR - 2025-07-20 16:53:03 --> Query error: Column 'rechecking_type' cannot be null - Invalid query: INSERT INTO `tbl_payment_subjects` (`payment_id`, `rechecking_type`, `subject_code`, `subject_name`) VALUES (15819,NULL,'BH1423','ENGLISH FOR TECHNICAL WRITING')
ERROR - 2025-07-20 17:52:12 --> Query error: Column 'rechecking_type' cannot be null - Invalid query: INSERT INTO `tbl_payment_subjects` (`payment_id`, `rechecking_type`, `subject_code`, `subject_name`) VALUES (15825,NULL,'MS6182','FINANCIAL MANAGEMENT')
ERROR - 2025-07-20 17:52:19 --> Query error: Column 'rechecking_type' cannot be null - Invalid query: INSERT INTO `tbl_payment_subjects` (`payment_id`, `rechecking_type`, `subject_code`, `subject_name`) VALUES (15826,NULL,'MS6182','FINANCIAL MANAGEMENT')
