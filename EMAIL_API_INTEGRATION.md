# Student Forgot Password - Email API Integration

## Overview

Successfully integrated the ZeptoMail API for sending password reset emails in the student forgot password feature, replacing the previous SMTP configuration with a more reliable API-based approach.

## Changes Made

### 1. ✅ Added Email API Constants

**File**: `application/config/constants.php`

Added the missing `MAIL_SENDER_ADDRESS` constant:
```php
defined('MAIL_SENDER_ADDRESS') || define('MAIL_SENDER_ADDRESS', "<EMAIL>");
```

**Existing Constants**:
- `MAIL_API_KEY`: ZeptoMail API key for authentication
- `MAIL_API_URL`: ZeptoMail API endpoint (`https://api.zeptomail.in/v1.1/email`)
- `MAIL_SENDER_ADDRESS`: Default sender email address

### 2. ✅ Added sendEmailApi Function

**File**: `application/controllers/Student.php`

Added the complete `sendEmailApi` function that:
- Uses cURL to send HTTP POST requests to ZeptoMail API
- Handles JSON payload formatting
- Includes proper error handling
- Returns boolean success/failure status

```php
public function sendEmailApi($mailsubject, $fromemail, $toemail, $toname, $msg_body, $cc_admin = null)
{
    $from_email = MAIL_SENDER_ADDRESS;
    $to_email = trim($toemail);
    
    $postFields = [
        "from" => ["address" => MAIL_SENDER_ADDRESS],
        "to" => [["email_address" => ["address" => $to_email, "name" => $toname]]],
        "subject" => $mailsubject,
        "htmlbody" => $msg_body,
    ];
    
    // cURL implementation with proper headers and authentication
    // Returns true on success, false on failure
}
```

### 3. ✅ Updated Student Forgot Password

**File**: `application/controllers/Student.php` - `sendpwd()` method

**Before**: Used temporary testing response without actual email sending
**After**: Integrated with ZeptoMail API for real email delivery

```php
// Generate email content
$msg = $this->load->view('_student/email', $data, TRUE);

// Send email using API
$mailsubject = 'Password Reset - Student Portal';
$fromemail = MAIL_SENDER_ADDRESS;
$toemail = $student_dt->email;
$toname = $student_dt->name;
$msg_body = $msg;

// Send email via API
if ($this->sendEmailApi($mailsubject, $fromemail, $toemail, $toname, $msg_body)) {
    $result[] = array('resSuccess' => 1, 'msg' => 'A reset link has been sent to your registered email. Please check your email and follow the link to reset your password.');
    echo json_encode($result);
    exit();
} else {
    $result[] = array('resSuccess' => 2, 'msg' => 'Unable to send email. Please try again later.');
    echo json_encode($result);
    exit();
}
```

## Email API Features

### ✅ ZeptoMail Integration:
- **API-based**: More reliable than SMTP
- **JSON payload**: Structured email data
- **Authentication**: Uses API key for secure access
- **Error handling**: Proper cURL error detection
- **HTML support**: Sends HTML formatted emails

### ✅ Email Content:
- **Subject**: "Password Reset - Student Portal"
- **From**: `<EMAIL>` (configurable via constant)
- **To**: Student's registered email address
- **Name**: Student's actual name from database
- **Body**: HTML template from `_student/email.php` view

### ✅ Security Features:
- **Token-based**: Uses MD5 hashed tokens for reset links
- **Encrypted URLs**: Registration numbers are MD5 hashed in URLs
- **Time-sensitive**: Tokens can be expired (implementation ready)
- **Single-use**: Tokens are cleared after password reset

## API Configuration

### Current Settings:
```php
// ZeptoMail API Configuration
MAIL_API_URL = "https://api.zeptomail.in/v1.1/email"
MAIL_API_KEY = "Zoho-enczapikey PHtE6r0MQOzu3m98oEIJ46LqEMasN4J//L81LglAtIdEA/MHGk1U/d0sxjW0+BktB/gREvSfm4pg5bOcteiMcDnkNT5PXmqyqK3sx/VYSPOZsbq6x00ctV4Td0PYVY7pd9Rr1CbUvdffNA=="
MAIL_SENDER_ADDRESS = "<EMAIL>"
```

### cURL Configuration:
- **Timeout**: 30 seconds
- **SSL Version**: TLS v1.2
- **HTTP Version**: 1.1
- **Content-Type**: application/json
- **Authorization**: API key based

## Testing Instructions

### 1. Test Forgot Password Flow
1. **Visit**: `http://localhost/outr_results/student/forgotpwd`
2. **Enter**: Valid registration number from database
3. **Submit**: Form should process successfully
4. **Check**: Student's email for password reset link

### 2. Verify Email Delivery
1. **Success Response**: Should show "A reset link has been sent to your registered email"
2. **Email Content**: Should contain reset link with proper formatting
3. **Reset Link**: Should work when clicked (format: `/student/resetpwd/{md5_regdno}/{token}`)

### 3. Test Error Handling
1. **Invalid Registration**: Should show "not registered" error
2. **API Failure**: Should show "Unable to send email" error
3. **Network Issues**: Should handle cURL errors gracefully

## Benefits of API Integration

### ✅ Reliability:
- **No SMTP configuration** required
- **Better delivery rates** than traditional SMTP
- **API-based monitoring** and logging available

### ✅ Scalability:
- **High throughput** email sending
- **Rate limiting** handled by API provider
- **Automatic retry** mechanisms

### ✅ Maintenance:
- **No server email setup** required
- **Centralized configuration** via constants
- **Easy to update** API credentials

### ✅ Security:
- **Encrypted transmission** via HTTPS
- **API key authentication**
- **No password storage** for email accounts

## Production Checklist

- ✅ **API Constants**: All email API constants defined
- ✅ **Function Integration**: sendEmailApi function added
- ✅ **Error Handling**: Comprehensive error responses
- ✅ **Email Templates**: HTML email template ready
- ✅ **Database Schema**: Token field exists in tbl_students
- ✅ **URL Generation**: Reset links properly formatted

The student forgot password feature now uses a professional email API service for reliable email delivery, replacing the previous SMTP-based approach with a more robust and maintainable solution.
