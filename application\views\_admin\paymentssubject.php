 <style media="print">
		.content-wrapper { background-color:#000;}
        .spn-br { display:block;}
 </style>
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        Rechecking/Retotalling
        
      </h1>
      <ol class="breadcrumb">
        <li><a href="<?php echo base_url('admin/dashboard'); ?>"><i class="fa fa-dashboard"></i> Dashboard</a></li>
        <li><a href="<?php echo base_url('admin/rechecking'); ?>">Rechecking/Retotaling</a></li>
        <li class="active">Subject List</li>
      </ol>
    </section>

    <div class="pad margin no-print">
      <div class="callout callout-info" style="margin-bottom: 0!important;">
        <h4><i class="fa fa-info"></i> Note:</h4>
        	<p>Rechecking/Retotalling of each answer script (subject) is &nbsp; <strong><i class="fa fa-inr"></i> 500</strong></p>
      		<p>Rechecking/Retotalling with Photocopy of each answer script is &nbsp;<strong><i class="fa fa-inr"></i> 1000</strong></p>
      </div>
    </div>
        <!-- Main content -->
        <section class="invoice">        
          <!-- title row -->
          <div class="row">          	
            <div class="col-xs-12">              	
              <h2 class="page-header">
                <span class="hidden-xs"> <i class="fa fa-globe"></i> <?php echo $payments->title; ?></span>&nbsp;
                <small class="pull-right">Date: <?php echo date('d-m-Y',strtotime($payments->payment_date)); ?></small>
              </h2>
            </div>
            <!-- /.col -->
          </div>
          <!-- info row -->
          <div class="row invoice-info">
            <div class="col-xs-12 col-sm-12 col-md-8  invoice-col">
              
              <address>
                Title: <strong><?php echo $payments->title; ?></strong><br>
                Stream: <strong><?php echo $payments->stream; ?></strong><br>
                Semester: <strong><?php echo $payments->semester; ?></strong><br>
              </address>
            </div>
            
            <div class="col-xs-12 col-sm-12 col-md-4 invoice-col">
             <address>
              <b>Registration No:&nbsp;</b><?php echo $payments->regd_no; ?><br>
              <b>Student Name:</b>&nbsp;<?php echo $payments->name ?><br>
              <?php if(!empty($payments->email)){ ?>	
              	<b>Email:</b>&nbsp;<?php echo $payments->email ?><br>
              <?php } ?>
              </address>
            </div>

    
          </div>
          <!-- /.row -->
    
          <!-- Table row -->
          <div class="row">
            <div class="col-xs-12 table-responsive">
              <table class="table table-striped">
                <thead>
                <tr>
                  <th>Subject Code</th>
                  <th>Subject Name</th>
                  <th style="text-align:center">Rechecking/Retotaling</th>
                  <th style="text-align:center">Rechecking/Retotaling <br/> with photocopy</th>
                  <th style="text-align:right;">Amount</th>
                </tr>
                </thead>
                <tbody>
                  <?php $i = 1; $grand_total = 0.00; foreach($subjects as $sub){   
				  			 if($sub->rechecking_type == 1){ 
							 		$grand_total += 500.00;
							 }else{
								 	$grand_total += 1000.00;
							 }
				  
				    ?>
                <tr>
                  <td><?php echo $sub->subject_code ?></td>
                  <td><?php echo $sub->subject_name ?></td>
                  <td align="center">
             		 <?php  echo $sub->rechecking_type == 1?'<i class="fa fa-check text-success" aria-hidden="true"></i>':'<i class="fa fa-times text-danger" aria-hidden="true"></i>'?></td>
                  <td align="center">
             		 <?php  echo $sub->rechecking_type == 2?'<i class="fa fa-check text-success" aria-hidden="true"></i>':'<i class="fa fa-times text-danger" aria-hidden="true"></i>'?></td>
                  </td>
                  <td align="right"><span class="sub_total_<?php echo $i ?>"><?php if($sub->rechecking_type == 1){ echo '500.00'; }else{ echo '1000.00'; }?></span></td>
                </tr>
                <?php $i++; } ?>
                
                </tbody>
              </table>
            </div>
            <!-- /.col -->
          </div>
          <!-- /.row -->
    
          <div class="row">
            <!-- accepted payments column -->
            <div class="col-xs-6">
            </div>
            <!-- /.col -->
            <div class="col-xs-6">
              
    
              <div class="table-responsive">
                <table class="table">
                  <tr>
                    <th align="right" style="width:50%;text-align:right;">No of subject selected:</th>
                    <td align="right"><span id="subject_count"><?php echo count($subjects); ?></span></td>
                  </tr>
   
                  <tr>
                    <th style="text-align:right;" >Total Amount:</th>
                    <td align="right"><i class="fa fa-inr"></i> <span id="grand_total"><?php echo number_format($payments->payment_amount,2); ?></span></td>
                  </tr>
                </table>
              </div>
            </div>
            <!-- /.col -->
          </div>
          <!-- /.row -->
    
          <!-- this row will not appear when printing -->
          <div class="row no-print">
            <div class="col-xs-12">
            </div>
          </div>
        </section>
    <!-- /.content -->
    <div class="clearfix"></div>
  </div>  