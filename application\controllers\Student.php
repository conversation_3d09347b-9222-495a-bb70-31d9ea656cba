<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Student extends CI_Controller
{

	function __construct()
	{
		parent::__construct();

		$this->load->helper('text');
		$this->load->library(array('encryption', 'form_validation'));
		$this->load->model('student_model', 'studentModel');
	}


	public function index()
	{
		$this->load->view('_student/login');
	}

	public function login()
	{
		$result = array();
		$this->form_validation->set_rules('regdno', "regdno", 'trim|required|callback__validate_login');
		if ($this->form_validation->run() == FALSE) {
			$result[] = array('resSuccess' => 2, 'msg' => 'Error', 'errtype' => 'Validation', 'arrError' => validation_errors());
			echo json_encode($result);
			exit();
		} else {
			$result[] = array('resSuccess' => 1, 'msg' => 'Success');
			echo json_encode($result);
			exit();
		}
	}

	public function _validate_login()
	{

		$regdno = trim($this->input->post("regdno"));
		$pwd = trim($this->input->post("password"));

		if ($regdno != "" && $pwd != "") {
			if ($this->studentModel->validate_login($regdno, $pwd)) {
				return true;
			} else {
				$this->form_validation->set_message('_validate_login', 'Unauthenticated User! Access Denied.');
				return false;
			}
		}
	}



	public function logout()
	{

		$this->session->sess_destroy();
		redirect('/');
	}

	public function dashboard()
	{

		$this->load->library('Student_acess');
		$data["menu"] = "dashboard";
		$this->load->view('_student_shared/header', $data);
		$this->load->view('_student/dashboard', $data);
		$this->load->view('_student_shared/footer', $data);
	}


	public function result()
	{

		$this->load->library('Student_acess');
		$data["menu"] = "result";

		$data['results'] = $this->studentModel->getResult();

		$this->load->view('_student_shared/header', $data);
		$this->load->view('_student/resultlist', $data);
		$this->load->view('_student_shared/footer', $data);
	}


	public function resultdetails($res_id)
	{
		$this->load->library('Student_acess');

		$data["menu"] = "result";
		$data['result'] = $this->studentModel->getStudentResult($res_id);

		//print_r($data['results']); die();
		//$this->load->view('_student_shared/header',$data);	
		$this->load->view('_student/result', $data);
		//$this->load->view('_student_shared/footer',$data);

	}


	public function changepwd()
	{
		$this->load->library('Student_acess');
		$data["menu"] = "";
		$this->load->view('_student_shared/header', $data);
		$this->load->view('_student/changepwd', $data);
		$this->load->view('_student_shared/footer', $data);
	}



	public function saveprofile()
	{
		$this->load->library('Student_acess');
		$data["menu"] = "";

		$name = $this->input->post('name', TRUE);
		$email = $this->input->post('email', TRUE);
		$old_password = $this->input->post('old_password', TRUE);
		$new_password = $this->input->post('new_password', TRUE);


		$this->db->trans_strict(TRUE);
		$this->db->trans_begin();

		$profile_info = $this->studentModel->getStudentdetails();
		if (!empty($profile_info)) {

			if ($this->encryption->decrypt($profile_info->password) != $old_password) {
				$result[] = array('resSuccess' => 2, 'msg' => 'Incorrect old password provided');
				echo json_encode($result);
				exit();
			} else {

				if (!empty($new_password)) {

					$data_pwd = array('password' => $this->encryption->encrypt($new_password));
					$this->studentModel->updateProfile($data_pwd);
				}
			}
		} else {
			$result[] = array('resSuccess' => 2, 'msg' => 'Profile not found.');
			echo json_encode($result);
			exit();
		}


		if ($this->db->trans_status() === TRUE) {
			$this->db->trans_commit();
			$result[] = array('resSuccess' => 1, 'msg' => 'Profile saved successfully');
			echo json_encode($result);
			exit();
		} else {
			$this->db->trans_rollback();
			$result[] = array('resSuccess' => 2, 'msg' => 'Unable to save profile');
			echo json_encode($result);
			exit();
		}
	}

	// Forgot Password Methods
	public function forgotpwd()
	{
		$data = array();
		$data['menu'] = '';
		$this->load->view('_student/forgot', $data);
	}

	public function sendpwd()
	{
		$data = array();
		$data['menu'] = '';
		$regdno = $this->input->post('regdno', TRUE); // Registration number only

		// Find student by registration number
		$student_dt = $this->studentModel->getStudentByRegdNo($regdno);

		if (!empty($student_dt)) {
			$regd_no = $student_dt->regd_no;
			$str_result = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
			$length_of_string = 5;
			$token = substr(str_shuffle($str_result), 0, $length_of_string);
			$token = md5($token);
			$data['id'] = md5($student_dt->regd_no);
			$data['token'] = $token;

			$data_token = array('token' => $token);
			$this->studentModel->updateToken($data_token, $regd_no);

			$msg = $this->load->view('_student/email', $data, TRUE);

			// Email configuration
			$config = array(
				'protocol' => 'smtp',
				'smtp_host' => 'ssl://smtp.googlemail.com',
				'smtp_port' => 465,
				'smtp_user' => '<EMAIL>', // Configure this
				'smtp_pass' => 'your-password', // Configure this
				'mailtype'  => 'html',
				'charset'   => 'iso-8859-1'
			);

			$this->load->library('email', $config);

			$from_email = $student_dt->email;
			$to_email = $student_dt->email; // Send to actual student email

			$this->email->from($from_email, 'OUTR - Student Result Portal');
			$this->email->to($to_email);
			$this->email->subject('Password Reset - Student Portal');
			$this->email->message($msg);

			//Send mail
			if (!$this->email->send()) {
				$result[] = array('resSuccess' => 2, 'msg' => $this->email->print_debugger());
				echo json_encode($result);
				exit();
			} else {
				$result[] = array('resSuccess' => 1, 'msg' => 'Success');
				echo json_encode($result);
				exit();
			}
		} else {
			$result[] = array('resSuccess' => 2, 'msg' => $regdno . ' is not registered.');
			echo json_encode($result);
			exit();
		}
	}

	public function resetpwd($id, $token)
	{
		$data['menu'] = '';
		$data['student_res'] = $this->studentModel->getStudentByMd5RegdNoAndToken($id, $token);

		if (!empty($data['student_res'])) {
			$this->load->view('_student/reset', $data);
		} else {
			$this->load->view('_student/error');
		}
	}

	public function updatepassword()
	{
		$regd_no = $this->input->post('regd_no', TRUE);
		$task = $this->input->post('task', TRUE);
		$token = $this->input->post('token', TRUE);
		$new_pwd = $this->input->post('newpassword', TRUE);

		$this->db->trans_strict(TRUE);
		$this->db->trans_begin();

		$data_pw = array(
			'password' => $this->encryption->encrypt($new_pwd),
			'token' => NULL
		);

		$this->studentModel->updatePassword($data_pw, $regd_no);

		if ($this->db->trans_status() === TRUE) {
			$this->db->trans_commit();
			$result[] = array('resSuccess' => 1, 'msg' => 'Password changed successfully');
			echo json_encode($result);
			exit();
		} else {
			$this->db->trans_rollback();
			$result[] = array('resSuccess' => 2, 'msg' => 'Unable to change password');
			echo json_encode($result);
			exit();
		}
	}
}
