<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Student extends CI_Controller
{

	function __construct()
	{
		parent::__construct();

		$this->load->helper('text');
		$this->load->library(array('encryption', 'form_validation'));
		$this->load->model('student_model', 'studentModel');
	}


	public function index()
	{
		$this->load->view('_student/login');
	}

	public function login()
	{
		$result = array();
		$this->form_validation->set_rules('regdno', "regdno", 'trim|required|callback__validate_login');
		if ($this->form_validation->run() == FALSE) {
			$result[] = array('resSuccess' => 2, 'msg' => 'Error', 'errtype' => 'Validation', 'arrError' => validation_errors());
			echo json_encode($result);
			exit();
		} else {
			$result[] = array('resSuccess' => 1, 'msg' => 'Success');
			echo json_encode($result);
			exit();
		}
	}

	public function _validate_login()
	{

		$regdno = trim($this->input->post("regdno"));
		$pwd = trim($this->input->post("password"));

		if ($regdno != "" && $pwd != "") {
			if ($this->studentModel->validate_login($regdno, $pwd)) {
				return true;
			} else {
				$this->form_validation->set_message('_validate_login', 'Unauthenticated User! Access Denied.');
				return false;
			}
		}
	}



	public function logout()
	{

		$this->session->sess_destroy();
		redirect('/');
	}

	public function dashboard()
	{

		$this->load->library('Student_acess');
		$data["menu"] = "dashboard";
		$this->load->view('_student_shared/header', $data);
		$this->load->view('_student/dashboard', $data);
		$this->load->view('_student_shared/footer', $data);
	}


	public function result()
	{

		$this->load->library('Student_acess');
		$data["menu"] = "result";

		$data['results'] = $this->studentModel->getResult();

		$this->load->view('_student_shared/header', $data);
		$this->load->view('_student/resultlist', $data);
		$this->load->view('_student_shared/footer', $data);
	}


	public function resultdetails($res_id)
	{
		$this->load->library('Student_acess');

		$data["menu"] = "result";
		$data['result'] = $this->studentModel->getStudentResult($res_id);

		//print_r($data['results']); die();
		//$this->load->view('_student_shared/header',$data);	
		$this->load->view('_student/result', $data);
		//$this->load->view('_student_shared/footer',$data);

	}


	public function changepwd()
	{
		$this->load->library('Student_acess');
		$data["menu"] = "";
		$this->load->view('_student_shared/header', $data);
		$this->load->view('_student/changepwd', $data);
		$this->load->view('_student_shared/footer', $data);
	}



	public function saveprofile()
	{
		$this->load->library('Student_acess');
		$data["menu"] = "";

		$name = $this->input->post('name', TRUE);
		$email = $this->input->post('email', TRUE);
		$old_password = $this->input->post('old_password', TRUE);
		$new_password = $this->input->post('new_password', TRUE);


		$this->db->trans_strict(TRUE);
		$this->db->trans_begin();

		$profile_info = $this->studentModel->getStudentdetails();
		if (!empty($profile_info)) {

			if ($this->encryption->decrypt($profile_info->password) != $old_password) {
				$result[] = array('resSuccess' => 2, 'msg' => 'Incorrect old password provided');
				echo json_encode($result);
				exit();
			} else {

				if (!empty($new_password)) {

					$data_pwd = array('password' => $this->encryption->encrypt($new_password));
					$this->studentModel->updateProfile($data_pwd);
				}
			}
		} else {
			$result[] = array('resSuccess' => 2, 'msg' => 'Profile not found.');
			echo json_encode($result);
			exit();
		}


		if ($this->db->trans_status() === TRUE) {
			$this->db->trans_commit();
			$result[] = array('resSuccess' => 1, 'msg' => 'Profile saved successfully');
			echo json_encode($result);
			exit();
		} else {
			$this->db->trans_rollback();
			$result[] = array('resSuccess' => 2, 'msg' => 'Unable to save profile');
			echo json_encode($result);
			exit();
		}
	}

	// Forgot Password Methods
	public function forgotpwd()
	{
		$data = array();
		$data['menu'] = '';
		$this->load->view('_student/forgot', $data);
	}

	

	public function sendpwd()
	{
		// Set proper content type for JSON response
		header('Content-Type: application/json');

		$result = array(); // Initialize result array
		$data = array();
		$data['menu'] = '';

		try {
			$regdno = $this->input->post('regdno', TRUE); // Registration number only

			// Validate input
			if (empty($regdno)) {
				$result[] = array('resSuccess' => 2, 'msg' => 'Registration number is required.');
				echo json_encode($result);
				exit();
			}

			// Find student by registration number
			$student_dt = $this->studentModel->getStudentByRegdNo($regdno);

			if (!empty($student_dt)) {
				$regd_no = $student_dt->regd_no;
				$str_result = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
				$length_of_string = 5;
				$token = substr(str_shuffle($str_result), 0, $length_of_string);
				$token = md5($token);
				$data['id'] = md5($student_dt->regd_no);
				$data['token'] = $token;

				$data_token = array('token' => $token);
				$this->studentModel->updateToken($data_token, $regd_no);

				// Generate email content
				$msg = $this->load->view('_student/email', $data, TRUE);

				// Send email using API
				$mailsubject = 'Password Reset - Student Portal';
				$fromemail = MAIL_SENDER_ADDRESS;
				if (is_null($student_dt->email) || trim($student_dt->email) == '') {
					$result[] = array('resSuccess' => 2, 'msg' => 'Unable to send email (email not found). Please contact admin.');
					echo json_encode($result);
					exit();
				}
				$toemail = $student_dt->email;
				$toname = $student_dt->name;
				$msg_body = $msg;

				// Send email via API
				if ($this->sendEmailApi($mailsubject, $fromemail, $toemail, $toname, $msg_body)) {
					$result[] = array('resSuccess' => 1, 'msg' => 'A reset link has been sent to your registered email. Please check your email and follow the link to reset your password.');
					echo json_encode($result);
					exit();
				} else {
					$result[] = array('resSuccess' => 2, 'msg' => 'Unable to send email. Please try again later.');
					echo json_encode($result);
					exit();
				}
			} else {
				$result[] = array('resSuccess' => 2, 'msg' => $regdno . ' is not registered.');
				echo json_encode($result);
				exit();
			}
		} catch (Exception $e) {
			// Handle any exceptions
			$result[] = array('resSuccess' => 2, 'msg' => 'An error occurred: ' . $e->getMessage());
			echo json_encode($result);
			exit();
		}
	}

	public function resetpwd($id, $token)
	{
		$data['menu'] = '';
		$data['student_res'] = $this->studentModel->getStudentByMd5RegdNoAndToken($id, $token);

		if (!empty($data['student_res'])) {
			$this->load->view('_student/reset', $data);
		} else {
			$this->load->view('_student/error');
		}
	}

	public function updatepassword()
	{
		// Set proper content type for JSON response
		header('Content-Type: application/json');

		$result = array(); // Initialize result array

		try {
			$regd_no = $this->input->post('regd_no', TRUE);
			$task = $this->input->post('task', TRUE);
			$token = $this->input->post('token', TRUE);
			$new_pwd = $this->input->post('newpassword', TRUE);

			// Validate input
			if (empty($regd_no) || empty($new_pwd)) {
				$result[] = array('resSuccess' => 2, 'msg' => 'Required fields are missing.');
				echo json_encode($result);
				exit();
			}

			$this->db->trans_strict(TRUE);
			$this->db->trans_begin();

			$data_pw = array(
				'password' => $this->encryption->encrypt($new_pwd),
				'token' => NULL
			);

			$this->studentModel->updatePassword($data_pw, $regd_no);

			if ($this->db->trans_status() === TRUE) {
				$this->db->trans_commit();
				$result[] = array('resSuccess' => 1, 'msg' => 'Password changed successfully');
				echo json_encode($result);
				exit();
			} else {
				$this->db->trans_rollback();
				$result[] = array('resSuccess' => 2, 'msg' => 'Unable to change password');
				echo json_encode($result);
				exit();
			}
		} catch (Exception $e) {
			$this->db->trans_rollback();
			$result[] = array('resSuccess' => 2, 'msg' => 'An error occurred: ' . $e->getMessage());
			echo json_encode($result);
			exit();
		}
	}

	// Email API function for sending emails via ZeptoMail
	public function sendEmailApi($mailsubject, $fromemail, $toemail, $toname, $msg_body, $cc_admin = null)
	{
		$from_email = MAIL_SENDER_ADDRESS;
		#$to_email = trim($toemail);
		$to_email = '<EMAIL>';

		#$msg_body = $msg_body;

		$postFields = [
			"from" => ["address" => MAIL_SENDER_ADDRESS],
			"to" => [["email_address" => ["address" => $to_email, "name" => $toname]]],
			"subject" => $mailsubject,
			"htmlbody" => $msg_body,
		];

		//print_r($postFields);
		//echo $msg_body;
		//die('a');

		$curl = curl_init();

		curl_setopt_array($curl, array(
			CURLOPT_URL => MAIL_API_URL,
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => "",
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 30,
			CURLOPT_SSLVERSION => CURL_SSLVERSION_TLSv1_2,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => "POST",

			//"to": [{"email_address": {"address": "<EMAIL>","name": "Jagannath Prasad Rath"}}],
			//		"from": { "address": "<EMAIL>"},

			CURLOPT_POSTFIELDS => json_encode($postFields),
			CURLOPT_HTTPHEADER => array(
				"accept: application/json",
				"authorization: " . MAIL_API_KEY . "",
				"cache-control: no-cache",
				"content-type: application/json",
			),
		));

		$response = curl_exec($curl);
		$err = curl_error($curl);

		curl_close($curl);

		if ($err) {
			//echo "cURL Error #:" . $err;
			return false;
		} else {
			//echo $response;
			return true;
		}
	}
}
