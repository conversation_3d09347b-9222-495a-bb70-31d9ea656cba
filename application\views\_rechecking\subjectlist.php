<style>
	.invoice{ position:relative;}
	.box-overlay{ 
		background:#000;
		width: 100%;
        height: 100%;            
        position: absolute;
        top: 0;
        left: 0;
        opacity: 0.8;  /* for demo purpose  */
		z-index: 999;
		
	}
	.box-overlay p{ color:#fff; top:50%; left:50%; transform:translateX(-50%); font-weight:bold; position:absolute; text-align:center; vertical-align:middle; font-size:18px;}
</style>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        Rechecking/Retotalling
        
      </h1>
      <ol class="breadcrumb">
        <li><a href="#"><i class="fa fa-dashboard"></i> Dashboard</a></li>
        <li><a href="#">Rechecking/Retotaling</a></li>
        <li class="active">Subject List</li>
      </ol>
    </section>

    <div class="pad margin no-print">
      <div class="callout callout-info" style="margin-bottom: 0!important;">
        <h4><i class="fa fa-info"></i> Note:</h4>
        	<p>Rechecking/Retotalling of each answer script (subject) is &nbsp; <strong><i class="fa fa-inr"></i> 500</strong></p>
      		<p>Rechecking/Retotalling with Photocopy of each answer script is &nbsp;<strong><i class="fa fa-inr"></i> 1000</strong></p>
      </div>
    </div>

	<form id="frmPayment" method="post">
        <!-- Main content -->
        <section class="invoice">
        	<div class="box-overlay" style="display:none;">
            	<p> <i class="fa fa-spinner fa-spin"></i><br />Please wait while we redirect you to the payment page, please do not refresh your page... </p>
            </div>
        
          <!-- title row -->
          <div class="row">
            <div class="col-xs-12">
              <h2 class="page-header">
                <span class="hidden-xs"> <i class="fa fa-globe"></i> <?php echo $result->title; ?></span>
                <small class="pull-right">Date: <?php echo date('d-m-Y'); ?></small>
              </h2>
            </div>
            <!-- /.col -->
          </div>
          <!-- info row -->
          <div class="row invoice-info">
            <div class="col-sm-7 invoice-col">
              
              <address>
                Title: <strong><?php echo $result->title; ?></strong><br>
                Stream: <strong><?php echo $result->stream; ?></strong><br>
                Semester: <strong><?php echo $result->semester; ?></strong><br>
              </address>
            </div>
            
            
            <div class="col-sm-5 invoice-col">
              <b>Student Name:</b>&nbsp;<?php echo $this->session->userdata('stu_sess_user_name') . ' (' . $this->session->userdata('stu_sess_user_id')  . ')'?><br>
              <b>Email:</b>&nbsp;<?php echo $this->session->userdata('stu_sess_user_email'); ?><br>
            </div>
            
    
          </div>
          <!-- /.row -->
    
          <!-- Table row -->
          <div class="row">
            <div class="col-xs-12 table-responsive">
              <table class="table table-striped">
                <thead>
                <tr>
                  <th></th>
                  <th>Subject Code</th>
                  <th>Subject Name</th>
                  <th>Rechecking/Retotalling</th>
                  <th>Rechecking/Retotalling with photocopy</th>
                  <th>Subtotal</th>
                </tr>
                </thead>
                <tbody>
                  <?php $i = 1; foreach($subjects as $sub){ ?>
                <tr>
                  <td><input id="rechecking_<?php  echo $i; ?>" onclick="enableRadio(<?php echo $i ?>)" name="recheck[]" value="<?php echo $sub->subject_code ?>-<?php  echo $i; ?>" type="checkbox"/></td>
                  <td><?php echo $sub->subject_code; ?></td>
                  <td><?php echo $sub->subject_name; ?></td>
                  <td align="center"><input type="radio" disabled="disabled" data-row="<?php echo $i; ?>" name="rechecking_<?php echo $i; ?>" class="rechecking_<?php echo $i; ?>" value="1"/></td>
                  <td align="center"><input type="radio" disabled="disabled" data-row="<?php echo $i; ?>" name="rechecking_<?php echo $i; ?>" class="rechecking_<?php echo $i; ?>" value="2"/></td>
                  <td><i class="fa fa-inr"></i> <span class="sub_total_<?php echo $i ?>">0.00</span></td>
                </tr>
                <?php $i++; } ?>
                
                </tbody>
              </table>
            </div>
            <!-- /.col -->
          </div>
          <!-- /.row -->
    
          <div class="row">
            <!-- accepted payments column -->
            <div class="col-xs-6">
              
              <img src="<?php echo base_url('assets/dist/img/billdesk.jpg') ?>" alt="Billdesk">
    
              <p class="text-muted well well-sm no-shadow" style="margin-top: 10px;">
                <b style="font-size:16px;">Note:</b><b style="color:#F00;"> Payment once made is not refundable. Payment process must be completed else it will be treated as failed payment.</b>
              </p>
            </div>
            <!-- /.col -->
            <div class="col-xs-6">
              
    
              <div class="table-responsive">
                <table class="table">
                  <tr>
                    <th style="width:50%">No of subject selected:</th>
                    <td><span id="subject_count">0</span></td>
                  </tr>
    <!--              <tr>
                    <th>Tax (9.3%)</th>
                    <td>$10.34</td>
                  </tr>
                  <tr>
                    <th>Shipping:</th>
                    <td>$5.80</td>
                  </tr>-->
                  <tr>
                    <th>Total Amount:</th>
                    <td><i class="fa fa-inr"></i> <span id="grand_total">0.00</span></td>
                  </tr>
                </table>
              </div>
            </div>
            <!-- /.col -->
          </div>
          <!-- /.row -->
    
          <!-- this row will not appear when printing -->
          <div class="row no-print">
            <div class="col-xs-12">
              <input type="hidden" name="result_id" value="<?php echo $result->id; ?>" />	
              <button type="submit" class="btn btn-success pull-right" id="btnsave"><i class="fa fa-credit-card"></i> Submit Payment </button>
            </div>
          </div>
        </section>
    </form>
    <!-- /.content -->
    <div class="clearfix"></div>
    <div class="row">
    	<form name="frmPayStep" id="frmPayStep" method="POST" action="https://pgi.billdesk.com/pgidsk/PGIMerchantPayment" class="form-horizontal" role="form">
        	<input type="hidden" name="msg" id="hdnmsg" value="" />
        </form>
    </div>
  </div>
  <!-- /.content-wrapper -->
  <script>
  
  
  var frmdata = [];
  
  
  
  
  $(document).ready(function(e) {
  
		 $('input[type="radio"]').change(function(e) {
			var row = $(this).attr('data-row'); 
			if($(this).val() == 1){
				$('.sub_total_' + row).html('500.00');
			}else if($(this).val() == 2){
				$('.sub_total_' + row).html('1000.00');
			}else{
				$('.sub_total_' + row).html('0.00');
			}
			 calculateTotal();
			
		 });
		 
	//------------------------	 
		 

	
			$('#frmPayment').submit(function(e) {
				
				e.preventDefault();

						$.ajax({
						type: "POST",
						url: "<?php echo base_url('saverechecking'); ?>",
						data: $("#frmPayment").serialize(),
						dataType: 'json',
						beforeSend: function (xhr) {
						  $("#btnsave").prop("disabled",true);
						  $("#btnsave").html('<i class="fa fa-spinner fa-spin"></i> Saving...');
						  $('.box-overlay').show();
						  
						},
						success: function (res_data) {
						 $('.box-overlay').hide();	
							if (res_data[0].resSuccess == 1) { 
								$('#frmPayStep #hdnmsg').val('').val(res_data[0].final_msg);
								$('#frmPayStep').submit();
							/*bootbox.alert({ 
								size: "small",
								message: 'Payment made successfully.',
								callback:function(){
										window.location.href="< ?php echo base_url('rechecking'); ?>";
									}
							});*/	

							
							
							/*bootbox.confirm({
									title: "Destroy planet?",
									message: "Do you want to activate the Deathstar now? This cannot be undone.",
									buttons: {
										cancel: {
											label: '<i class="fa fa-times"></i> Cancel'
										},
										confirm: {
											label: '<i class="fa fa-check"></i> Confirm'
										}
									},
									callback: function (result) {
										console.log('This was logged in the callback: ' + result);
									}
							});*/
							
							
							}else if (res_data[0].resSuccess == 2){
								  $("#btnsave").prop("disabled",false);
								   $("#btnsave").html('Submit Payment');
								   bootbox.alert({ 
										size: "small",
										message: res_data[0].msg,
										callback:function(){
												//window.location.href=window.location.href;
										}
									});	
						
									return false;
								}					
							}
						});

				return false;
				});
		 
		 
	//---------------------	 
		
	});
  
  	function enableRadio(vl){
		if($("#rechecking_" + vl ).is(':checked')){ 
			$(".rechecking_" + vl).attr('disabled',false);
			$(".rechecking_" + vl).eq(0).trigger('click');
		}else{ 
			$('.sub_total_' + vl).html('0.00')
			$(".rechecking_" + vl).prop('checked',false);
			$(".rechecking_" + vl).attr('disabled',true);
		}
	}
	
	
	function calculateTotal(){
		var total = 0.00;
		var sub_total = 0.00;
		var sub_count = 0;
		$('input:radio').each(function() {
		  if($(this).is(':checked')) {
			  sub_count++;
			if($(this).val() == 1){
				 sub_total += 500.00;
			}else{
				sub_total += 1000.00;
			}
		  } 
		  else {
			// Or an unchecked one here...
		  }
		});
		$('#subject_count').html(sub_count);
		$('#grand_total').html(sub_total.toFixed(2));
		
	}
  

  </script>